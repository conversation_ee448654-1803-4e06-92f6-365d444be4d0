CREATE TABLE `alert_range_notification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cmp_id` bigint(20) NOT NULL,
  `gateway_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `alerttype_id` bigint(20) NOT NULL,
  `pn_datetime` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `mail_datetime` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `pn_content` text,
  `mail_content` text,
  `created_at` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `remarks` varchar(250) NOT NULL,
  `alert_value` float NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_gateway_alerttype` (`gateway_id`,`alerttype_id`,`created_at`)
);

CREATE TABLE `alertcount_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `alert_date` date NOT NULL,
  `cmp_id` bigint(20) NOT NULL,
  `gateway_id` bigint(20) NOT NULL,
  `temp_mail` int(11) NOT NULL DEFAULT '0',
  `temp_sms` int(11) NOT NULL DEFAULT '0',
  `temp_notify` int(11) NOT NULL DEFAULT '0',
  `hum_mail` int(11) NOT NULL DEFAULT '0',
  `hum_sms` int(11) NOT NULL DEFAULT '0',
  `hum_notify` int(11) NOT NULL DEFAULT '0',
  `pl_mail` int(11) NOT NULL DEFAULT '0',
  `pl_sms` int(11) NOT NULL DEFAULT '0',
  `pl_notify` int(11) NOT NULL DEFAULT '0',
  `pb_mail` int(11) NOT NULL DEFAULT '0',
  `pb_sms` int(11) NOT NULL DEFAULT '0',
  `pb_notify` int(11) NOT NULL DEFAULT '0',
  `bat_mail` int(11) NOT NULL DEFAULT '0',
  `bat_sms` int(11) NOT NULL DEFAULT '0',
  `bat_notify` int(11) NOT NULL DEFAULT '0',
  `dnr_mail` int(11) NOT NULL DEFAULT '0',
  `dnr_sms` int(11) NOT NULL DEFAULT '0',
  `dnr_notify` int(11) NOT NULL DEFAULT '0',
  `geo_mail` int(11) NOT NULL DEFAULT '0',
  `geo_sms` int(11) NOT NULL DEFAULT '0',
  `geo_notify` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_date_cmp_gateway` (`alert_date`,`cmp_id`,`gateway_id`)
);