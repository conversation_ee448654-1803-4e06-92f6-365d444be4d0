buildscript {
    repositories {
       // maven { url "https://repo.spring.io/libs-release" }
        maven { url "https://repo.spring.io/milestone" }
        mavenLocal()
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:2.1.3.RELEASE")
    }
}
apply plugin: 'java'
apply plugin: 'war'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'jacoco'
sourceCompatibility = '1.8'
targetCompatibility = '1.8'
[compileJava, compileTestJava]*.options*.encoding = 'UTF-8'
//task wrapper(type: Wrapper) {
//  gradleVersion = '5.6.2'
//}
war {
    enabled = true
    baseName = 'wgalertsvc'
    //version =  '3.0.0-SNAPSHOT'
}
repositories {
	mavenCentral()
}
dependencies {
   // Upgrade all the dependencies to Latest version
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-web', version: '2.1.3.RELEASE'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-web-services', version: '2.1.3.RELEASE'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-aop', version: '2.1.3.RELEASE'
    testCompile group: 'org.springframework.boot', name: 'spring-boot-starter-test', version: '2.1.3.RELEASE'
	compile group: 'org.springframework', name: 'spring-orm', version: '4.3.17.RELEASE'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-freemarker', version: '2.1.3.RELEASE'
    compile group: 'org.hibernate', name: 'hibernate-core', version: '4.3.6.Final'
    compile group: 'mysql', name: 'mysql-connector-java', version: '8.0.33'
    //compile group: 'com.mysema.querydsl', name: 'querydsl-hql', version: '1.8.2'
    compile group: 'commons-lang', name: 'commons-lang', version: '2.4'
    compile group: 'org.apache.commons', name: 'commons-io', version: '1.3.2'                   	         
	compile group: 'org.apache.commons', name: 'commons-lang3', version: '3.1'
    compile group: 'org.apache.commons', name: 'commons-io', version: '1.3.2'
  	compile group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.2'
  	compile group: 'org.apache.httpcomponents', name: 'httpcore', version: '4.4.4'  	
  	compile group: 'commons-codec', name: 'commons-codec', version: '1.6'
    compile group: 'org.apache.logging.log4j', name: 'log4j-api', version: '2.17.1'
    compile group: 'org.apache.logging.log4j', name: 'log4j-core', version: '2.17.1'
    compile group: 'org.apache.logging.log4j', name: 'log4j-web', version: '2.17.1'
  	compile group: 'org.apache.poi', name: 'poi', version: '3.6'	
		
	//compile group: 'log4j', name: 'log4j', version: '1.2.16'
	compile group: 'com.google.code.gson', name: 'gson', version: '2.2.2'
    compile group: 'com.lmax', name: 'disruptor', version: '3.4.2'
    compile group: 'com.mchange', name: 'c3p0', version: '0.9.5'
  	compile group: 'org.json', name: 'json', version: '20140107'
  	compile group: 'org.ocpsoft.prettytime', name: 'prettytime', version: '3.2.7.Final'
  	compile group: 'org.codehaus.jackson', name: 'jackson-core-asl', version: '1.9.12'
  	compile group: 'org.codehaus.jackson', name: 'jackson-mapper-asl', version: '1.9.12'
  	compile group: 'com.opencsv', name: 'opencsv', version: '4.1'
  	compile group: 'javax.mail', name: 'mail', version: '1.4.7'
  	
  	compile group: 'com.google.firebase', name: 'firebase-admin', version: '9.2.0'
  	
  	compile group: 'com.plivo', name: 'plivo-java', version: '4.4.0'
	
	// Dependencies for Quartz
	compile group: 'org.quartz-scheduler', name: 'quartz', version: '2.3.0'
	compile group: 'org.quartz-scheduler', name: 'quartz-jobs', version: '2.3.0'

    // Dependencies for AWS Secret manager
    compile 'software.amazon.awssdk:secretsmanager:2.20.81'

    // Dependencies for Caffeine cache
    compile 'com.github.ben-manes.caffeine:caffeine:2.9.3'

	providedCompile group: 'javax.servlet', name: 'javax.servlet-api', version: '4.0.1'


    implementation group: 'io.springfox', name: 'springfox-boot-starter', version: '3.0.0'
    implementation group: 'io.springfox', name: 'springfox-swagger2', version: '3.0.0'
    implementation group: 'io.springfox', name: 'springfox-swagger-ui', version: '3.0.0'

    // JUnit Jupiter (JUnit 5)
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.0'

    // Mockito Core
    testImplementation 'org.mockito:mockito-core:3.12.4'

    // Mockito JUnit Jupiter Integration
    testImplementation 'org.mockito:mockito-junit-jupiter:3.12.4'

}
configurations.all {
 exclude module: 'log4j-to-slf4j'
 exclude module: 'slf4j-log4j12'
}
test {
    useJUnitPlatform()
    enabled = true

    // Show test results in console
    testLogging {
        events "passed", "skipped", "failed"    // log all results
        exceptionFormat "full"                  // full stacktrace for failures
        showStandardStreams = true              // show System.out and System.err
    }

    // Make Jacoco generate coverage after tests
    finalizedBy jacocoTestReport
}


jacoco {
    toolVersion = "0.8.10"
}

jacocoTestReport {
    dependsOn test  // tests must run before generating report

    reports {
        xml.required = true
        html.required = true
        csv.required = false
    }
}
