{"info": {"_postman_id": "02bfd8db-7081-4d23-8b04-c4c035c1dd20", "name": "schedulerservice", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "setschedulealert", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": 0,\r\n    \"alert_id\": 0,\r\n    \"gateway_id\": 12212,\r\n    \"cmp_id\": 3769,\r\n    \"enable\": 1,\r\n    \"mobilenos\": \"918056958031\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"created_at\": \"2021-10-06 07:03:37\",\r\n    \"updated_at\": \"2021-10-06 07:03:37\",\r\n    \"scheduler_name\": \"12212-16335038173120\",\r\n    \"scheduler_time\": \"2021-10-06 08:23:21\",\r\n    \"cmp_name\": \"webhooks-1632979059699\",\r\n    \"sms_msg\": \"RV Power Loss Alert,\\nMonitor: webhooks,\\nBattery: 64%,\\nTime:6 Oct 2021,12:32 PM,+05:30\",\r\n    \"email_msg\": \"RV Power Loss Alert,\\nMonitor: webhooks,\\nBattery: 64%,\\nTime:6 Oct 2021,12:32 PM,+05:30\",\r\n    \"gateway_name\": \"webhooks\",\r\n    \"alertcfg_id\": 17419,\r\n    \"notify_freq\": 3600,\r\n    \"timezone\": \"+05:30\",\r\n    \"pkt_datetime\": \"2021-10-06 12:32:41\",\r\n    \"noofsmsalerts\": 2,\r\n    \"creditspent\": 9,\r\n    \"noofemailalerts\": 0,\r\n    \"extracreditspent\": 0,\r\n    \"alerttype_id\": 3,\r\n    \"alert_enable\": true,\r\n    \"alertvalue\": 64.0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://staging-api.nimblepetapp.com/schedulerservice/v4.0/setschedulealert", "protocol": "http", "host": ["staging-api", "nimblepetapp", "com"], "path": ["schedulerservice", "v4.0", "setschedulealert"]}}, "response": [{"name": "Prod-setschedulealert", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": 0,\r\n    \"alert_id\": 1206475,\r\n    \"gateway_id\": 21306,\r\n    \"cmp_id\": 19090,\r\n    \"enable\": 1,\r\n    \"mobilenos\": \"918056958031,919688746658,919944264302,919486867048,919659681409\",\r\n    \"email\": \"<EMAIL>,<EMAIL>,<EMAIL>,mbal<PERSON><EMAIL>,<EMAIL>\",\r\n    \"created_at\": \"2021-09-15 13:43:58\",\r\n    \"updated_at\": \"2021-09-15 13:43:58\",\r\n    \"scheduler_name\": \"karthik<PERSON><PERSON>\",\r\n    \"scheduler_time\": \"2021-10-05 13:08:00\",\r\n    \"cmp_name\": \"Suresh_5384263-1629445328525\",\r\n    \"sms_msg\": \"RV Power Loss Alert,\\nGateway: TestPQA,\\nValue: 33%,\\nTime:3 Sep 2021,05:13 PM,+05:30\\\\nGet Your $20 Gift http://bit.ly/wagref\",\r\n    \"email_msg\": \"Test\",\r\n    \"gateway_name\": \"Test\",\r\n    \"alertcfg_id\": 94887,\r\n    \"notify_freq\": 60,\r\n    \"timezone\": \"+05:30\",\r\n    \"delaytime\": 40,\r\n    \"noofsmsalerts\": 1,\r\n    \"creditspent\": 1,\r\n    \"noofemailalerts\": 0,\r\n    \"extracreditspent\": 2,\r\n    \"alerttype_id\": 3,\r\n    \"alert_enable\": true,\r\n    \"alertvalue\": 33.0,\r\n    \"pkt_datetime\" : \"2021-10-05 13:00:00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://api.nimblepetapp.com/schedulerservice/v4.0/setschedulealert", "protocol": "http", "host": ["api", "nimblepetapp", "com"], "path": ["schedulerservice", "v4.0", "setschedulealert"]}}, "_postman_previewlanguage": null, "header": null, "cookie": [], "body": null}]}, {"name": "deleteschedu<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": 0,\r\n    \"alert_id\": 0,\r\n    \"gateway_id\": 12212,\r\n    \"cmp_id\": 3769,\r\n    \"enable\": 1,\r\n    \"mobilenos\": \"919659428385,16503523584\",\r\n    \"email\": \"ka<PERSON><PERSON><PERSON>@nimblewireless.com,<EMAIL>\",\r\n    \"created_at\": \"2021-10-06 07:03:37\",\r\n    \"updated_at\": \"2021-10-06 07:03:37\",\r\n    \"scheduler_name\": \"12212-1633503817350\",\r\n    \"scheduler_time\": \"2021-10-06 07:16:21\",\r\n    \"cmp_name\": \"webhooks-1632979059699\",\r\n    \"sms_msg\": \"RV Power Loss Alert,\\nMonitor: webhooks,\\nBattery: 64%,\\nTime:6 Oct 2021,12:32 PM,+05:30\",\r\n    \"email_msg\": \"RV Power Loss Alert,\\nMonitor: webhooks,\\nBattery: 64%,\\nTime:6 Oct 2021,12:32 PM,+05:30\",\r\n    \"gateway_name\": \"webhooks\",\r\n    \"alertcfg_id\": 17419,\r\n    \"notify_freq\": 3600,\r\n    \"timezone\": \"+05:30\",\r\n    \"pkt_datetime\": \"2021-10-06 12:32:41\",\r\n    \"noofsmsalerts\": 2,\r\n    \"creditspent\": 9,\r\n    \"noofemailalerts\": 0,\r\n    \"extracreditspent\": 0,\r\n    \"alerttype_id\": 3,\r\n    \"alert_enable\": true,\r\n    \"alertvalue\": 64.0\r\n}"}, "url": {"raw": "http://localhost:8080/schedulerservice/v4.0/deleteschedulealert?schedulername=12212-1633503817350", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["schedulerservice", "v4.0", "deleteschedu<PERSON><PERSON><PERSON>"], "query": [{"key": "<PERSON><PERSON><PERSON>", "value": "12212-1633503817350"}]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8080/schedulerservice/v4.0/SchedulerCheck?create=true&intervel=10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["schedulerservice", "v4.0", "<PERSON>r<PERSON><PERSON><PERSON>"], "query": [{"key": "create", "value": "true"}, {"key": "intervel", "value": "10"}]}}, "response": []}, {"name": "schedulerdelete", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://staging-api.nimblepetapp.com/schedulerservice/v4.0/Schedulerdelete", "protocol": "http", "host": ["staging-api", "nimblepetapp", "com"], "path": ["schedulerservice", "v4.0", "Schedulerdelete"]}}, "response": []}]}