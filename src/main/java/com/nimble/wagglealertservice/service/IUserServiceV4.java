package com.nimble.wagglealertservice.service;

import java.util.List;

import com.nimble.wagglealertservice.dto.JUser;
import com.nimble.wagglealertservice.dto.UserV4;
import com.nimble.wagglealertservice.exception.InvalidAuthoException;

public interface IUserServiceV4 {
	
	public UserV4 verifyAuthV4(String key,String value) throws InvalidAuthoException;
	
	public JUser getUserPlanVersion(long gatewayId);
	
	long[] getCreditPoints();

	long[] getNoOfEmailsAndMobilesConf(int companyId);

	boolean getAppNotifyEnable(int companyId);

	List<String> getTokensByGatewayId(long gatewayId);

	int getCompanyType(long companyId);

	boolean isVoiceAlert(long alertCfgId, String startDateTime);

	public String getAlertBasedon();
	
	public String getTempUnitByCmpy(long cmp_id);

	boolean checkFlexiPlanHistory(long gatewayId);
}	
