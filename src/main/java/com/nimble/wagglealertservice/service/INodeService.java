package com.nimble.wagglealertservice.service;

import java.util.ArrayList;

import com.nimble.wagglealertservice.dto.JLastNodeRpt;
import com.nimble.wagglealertservice.dto.JNode;

public interface INodeService {

	public ArrayList<JLastNodeRpt> getLastNodeRpt();

	public boolean updateNodeStatus(long nodeId, int notReporting);

	public JNode getNode(long nodeId);

	public boolean isNodeEnabled(long nodeId);
	
}
