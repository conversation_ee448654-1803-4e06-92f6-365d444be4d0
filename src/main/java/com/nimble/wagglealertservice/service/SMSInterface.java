package com.nimble.wagglealertservice.service;

import org.apache.http.entity.StringEntity;

/*
 * This interface encapsulates the sms gateway that we are using.
 * So in future, we can use more than one sms gateway.
 */
public interface SMSInterface {

	boolean SendVoiceMessage(String phoneNo, String message, int cmpId, String cmpName, String ip);
    
    public boolean callIrisSmsAPI(String phoneno, String msg, int cmpid, String cmpname, String ip,String validate_auth);
    
	public boolean callIrisEmailAPI(String emailds, String sub, String message, String ip, boolean isRvEmailId, String validate_auth);

	public boolean callAPIPostMethod(String url);
	
	public boolean postAPIWithAuthHeader(String url, String auth,StringEntity postingString);

	public String callAPIGetMethod(String sub_url, String auth);

	public String postAPIWithAuthHeaderReturnstring(String url, String auth,StringEntity postingString);

	public String callIrisSmsAPIReturnString(String phoneno, String msg, int cmpid, String cmpname, String ip,String validate_auth);

}
