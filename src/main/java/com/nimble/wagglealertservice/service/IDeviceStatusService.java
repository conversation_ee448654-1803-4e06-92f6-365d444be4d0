package com.nimble.wagglealertservice.service;

import java.util.ArrayList;

import com.nimble.wagglealertservice.dto.*;

public interface IDeviceStatusService {

	public ArrayList<JLastGatewayRpt> getLastGatewayReport(int count);

	public boolean isNRAlert(long companyId, String currentDateTime, long gatewayId);

	public AlertCfgV4 getAlertCfg(long assetId, long companyId,long alerttype_id);

	public JAlert getAlert(long alertCfgId,long assetId);

	public boolean updateAlert(long alertCfgId, String endDateTime, int count, long assetId);

	public boolean insertAlert(String startDateTime, String endDateTime, String timeZone, 
             float alertValue, String unit,double lat, String latDir, double lon, String longDir, String address, String gpsstatus,
             float battStatus, int rawrssi, String rssi, float externalsensor, float humidity, float temperature, float light,
             String motion, float pressure,long alerttypeId, long assetId, long gatewayId, long nodeId, 
            long alertCfgId, long companyId);

	public long getAlertId(String startDateTime, long alertCfgId);

	public long[] getCreditsInfoForGateway(int gatewayId, int companyId);

	public boolean updateThrottlingCount(long gatewayId, String gatewayname, long companyId,
			JGateway asset);

	public boolean updatealertnotifyhistory(String endDateTime, String timeZone, long alertId,int notifyFreq);

	public boolean updateGatewayStatus1(long gatewayId, int notReporting);

	public long getUserGateway(long gatewayId);
	
	public boolean updateDailyAlertCount(JDailyAlertTypeCount jdatCount);
	
	public boolean updateGatewayShowVideo(long gatewayid, boolean isShow);
	
	public long getMonitortype( long gatewayId);

    boolean checkLastCreditNotification(long gatewayId,String featurecodeSms, JUser juser);

    long getAlertPushNotificationId(String featurecodeSms);

	boolean checkIsThreadSensor(long gatewayId);

	long getHubIdForSensor(long nodeId);
}
