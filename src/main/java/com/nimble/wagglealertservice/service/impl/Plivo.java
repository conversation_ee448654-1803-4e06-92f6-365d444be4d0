package com.nimble.wagglealertservice.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URLEncoder;

import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimble.wagglealertservice.service.SMSInterface;

@Service
@Transactional
public class Plivo implements SMSInterface {

	private static final Logger log = LogManager.getLogger(Plivo.class);
	
	@Override
	public boolean callIrisSmsAPI(String phoneno, String msg, int cmpid, String cmpname, String ipaddress,
			String validate_auth) {

		try {

			String msg_enc = URLEncoder.encode(msg.trim(), "UTF-8");
			String cmp_enc = URLEncoder.encode(cmpname.trim(), "UTF-8");
			String smsurl = ipaddress + "/irisservices/v3.0/nimblesms?" + "phoneno=" + phoneno + "&msg="
					+ msg_enc + "&cmpid=" + cmpid + "&cmpname=" + cmp_enc + "&appname=Iris3.0&type=sms";
			log.info("SMSUrl:" + smsurl);
			//return callAPIPostMethod(smsurl);
			System.out.println(smsurl);

			return postAPIWithAuthHeader(smsurl, validate_auth, null);

		} catch (Exception e) {
			log.error("callIrisSmsAPI Exception :" + e);
		}
		return false;
	}

//	public boolean SendMessage(String phoneNo, String message, String cmpid, String cmpname, String appname,
//			String plivono, boolean enablePowerBack, String powerBackUUID) {
//		try {
//			PlivoClient client = new PlivoClient(authId, authToken);
//			phoneNo = phoneNo.replace(",", "<");
//			List<String> destPhList = Collections.singletonList(phoneNo);
//
//			MessageCreateResponse response = null;
//
//			if (enablePowerBack) {
//				response = com.plivo.api.models.message.Message.creator(destPhList, message, powerBackUUID)
//						.client(client).create();
//
//			} else {
//				response = com.plivo.api.models.message.Message.creator(plivono, destPhList, message).client(client)
//						.create();
//			}
//			log.info("msg sent details : " + response);
//		} catch (Exception e) {
//			log.error("SendMessage: PlivoException = " + e.getLocalizedMessage());
//			return false;
//		}
//		return true;
//	}
//
	@Override
	public boolean SendVoiceMessage(String phoneNo, String message, int cmpId, String cmpName, String ipaddress) {

		try {
			String msg_enc = URLEncoder.encode(message.trim(), "UTF-8");
			String cmp_enc = URLEncoder.encode(cmpName.trim(), "UTF-8");

			String voiceurl = ipaddress + "/irisservices/v3.0/nimblevoice?msg=" + msg_enc + "&phoneno="
					+ phoneNo + "&cmpid=" + cmpId + "&cmpname=" + cmp_enc + "&appname=Iris3.0&type=voice&ip="
					+ ipaddress;

			log.info("VoiceUrl:" + voiceurl);
			return callAPIPostMethod(voiceurl);

		} catch (Exception e) {
			log.error("SendVoiceMessage Exception :" + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean callIrisEmailAPI(String emailds, String sub, String message, String ip, boolean isRvEmailId,
			String validate_auth) {

		try {

			String msg_enc = URLEncoder.encode(message.trim(), "UTF-8");
			String sub_enc = URLEncoder.encode(sub.trim(), "UTF-8");
			String url = ip + "/irisservices/v3.0/nimbleemail?" + "emailids=" + emailds + "&subject="
					+ sub_enc + "&content=" + msg_enc + "&isRvEmailId=" + isRvEmailId;

			log.info("EmailUrl:" + url);
			//return callAPIPostMethod(url);
			return postAPIWithAuthHeader(url, validate_auth, null);

		} catch (Exception e) {
			log.error("callIrisEmailAPI Exception :" + e);
		}
		return false;
	}

	@Override
	public boolean callAPIPostMethod(String url) {
		CloseableHttpClient client = HttpClientBuilder.create().build();
		int timeout = 5 * 1000; // 5 seconds
		try {

			HttpPost post = new HttpPost(url);

			Builder requestConfigBuilder = RequestConfig.custom();
			requestConfigBuilder.setConnectionRequestTimeout(timeout);// http.connection.timeout
			requestConfigBuilder.setSocketTimeout(timeout);

			post.setConfig(requestConfigBuilder.build());
			HttpResponse response = client.execute(post);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			rd.close();

			log.info("result: " + result.toString());

		} catch (IOException e) {
			
			log.error("callAPI Exception:" + e);
		} catch (Exception e) {
			
			log.error("callAPI Exception :" + e);
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				log.error("callAPI Exception :" + e);
			}
		}

		return true;
	}

	@Override
	public boolean postAPIWithAuthHeader(String url, String auth,StringEntity postingString) {
		CloseableHttpClient client = HttpClientBuilder.create().build();
		int timeout = 5 * 1000; // 5 seconds
		try {
			
			HttpPost post = new HttpPost(url);
			post.setHeader("Content-type", "application/json");
			
			if(auth != null)
				post.setHeader("auth",auth);
			
			if (postingString != null ) {				
				post.setEntity(postingString);
			}
						
			Builder requestConfigBuilder = RequestConfig.custom();
			requestConfigBuilder.setConnectionRequestTimeout(timeout);// http.connection.timeout
			requestConfigBuilder.setSocketTimeout(timeout);

			post.setConfig(requestConfigBuilder.build());
			HttpResponse response = client.execute(post);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			rd.close();

			log.info("result: " + result.toString());

		} catch (IOException e) {
			e.printStackTrace();
			log.error("postAPIWithAuthHeader :" + e);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("postAPIWithAuthHeader :" + e);
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				log.error("postAPIWithAuthHeader :" + e);
			}
		}

		return true;
	}

	@Override
	public String callAPIGetMethod(String url, String auth) {
		CloseableHttpClient client = HttpClientBuilder.create().build();
		int timeout = 5 * 1000; // 5 seconds
		try {

			HttpGet post = new HttpGet(url);
			post.addHeader("auth",auth);
			HttpResponse response = client.execute(post);

			Builder requestConfigBuilder = RequestConfig.custom();
			requestConfigBuilder.setConnectionRequestTimeout(timeout);// http.connection.timeout
			requestConfigBuilder.setSocketTimeout(timeout);

			post.setConfig(requestConfigBuilder.build());

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			rd.close();

			log.info("result: " + result.toString());
			
			return result.toString();

		} catch (IOException e) {
			
			log.error("postAPIWithAuthHeader :" + e);
		} catch (Exception e) {
			
			log.error("postAPIWithAuthHeader :" + e);
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				log.error("postAPIWithAuthHeader :" + e);
			}
		}

		return null;
	}

	@Override
	public String callIrisSmsAPIReturnString(String phoneno, String msg, int cmpid, String cmpname, String ipaddress,
								  String validate_auth) {

		try {

			String msg_enc = URLEncoder.encode(msg.trim(), "UTF-8");
			String cmp_enc = URLEncoder.encode(cmpname.trim(), "UTF-8");
            ipaddress = "http://localhost:8080";
			String smsurl = ipaddress + "/irisservices/v4.0/nimblesms?" + "phoneno=" + phoneno + "&msg="
					+ msg_enc + "&cmpid=" + cmpid + "&cmpname=" + cmp_enc + "&appname=Iris3.0&type=sms";
			log.info("SMSUrl:" + smsurl);
			//return callAPIPostMethod(smsurl);

			return postAPIWithAuthHeaderReturnstring(smsurl, validate_auth, null);

		} catch (Exception e) {
			log.error("callIrisSmsAPI Exception :" + e);
		}
		return "NA";
	}

	@Override
	public String postAPIWithAuthHeaderReturnstring(String url, String auth,StringEntity postingString) {
		CloseableHttpClient client = HttpClientBuilder.create().build();
		int timeout = 120 * 1000; // 5 seconds
		try {

			HttpPost post = new HttpPost(url);
			post.setHeader("Content-type", "application/json");

			if (auth != null)
				post.setHeader("auth", auth);

			if (postingString != null) {
				post.setEntity(postingString);
			}

			Builder requestConfigBuilder = RequestConfig.custom();
			requestConfigBuilder.setConnectionRequestTimeout(timeout);// http.connection.timeout
			requestConfigBuilder.setSocketTimeout(timeout);

			post.setConfig(requestConfigBuilder.build());
			HttpResponse response = client.execute(post);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			rd.close();

			log.info("API Response: " + result.toString());

			            // Parse JSON response to extract MsgId
            try {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode rootNode = mapper.readTree(result.toString());

                // Debug log the complete response
                log.info("Complete API Response: " + rootNode.toPrettyString());

                // Check if response has a nested 'response' object
                if (rootNode.has("response")) {
                    JsonNode responseNode = rootNode.get("response");
                    if (responseNode.has("Status") &&
                        responseNode.get("Status").asInt() == 1 &&
                        responseNode.has("MsgId")) {

                        String msgId = responseNode.get("MsgId").asText();
                        log.info("Successfully extracted MsgId from response: " + msgId);
                        return msgId;
                    } else {
                        String errorMsg = responseNode.has("Msg") ?
                            responseNode.get("Msg").asText() : "Unknown error";
                        log.error("API response contains error. Status: " +
                                (responseNode.has("Status") ? responseNode.get("Status") : "N/A") +
                                ", Message: " + errorMsg);
                    }
                }
			} catch (JsonProcessingException e) {
				log.error("Error parsing API response: " + e.getMessage() + ". Response: " + result, e);
			}

		} catch (IOException e) {
		} catch (Exception e) {
			log.error("postAPIWithAuthHeader error: " + e.getMessage(), e);
		} finally {
			try {
				if (client != null) {
					client.close();
				}
			} catch (Exception e) {
				log.error("Error closing HTTP client: " + e.getMessage(), e);
			}
		}

		return "NA";
	}
	}
