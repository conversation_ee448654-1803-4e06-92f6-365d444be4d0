package com.nimble.wagglealertservice.service.impl;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.wagglealertservice.dao.INodeDao;
import com.nimble.wagglealertservice.dto.JLastNodeRpt;
import com.nimble.wagglealertservice.dto.JNode;
import com.nimble.wagglealertservice.service.INodeService;

@Service
@Transactional
public class NodeServiceImpl implements INodeService{

	@Autowired
	INodeDao nodeDao;
	
	@Override
	public ArrayList<JLastNodeRpt> getLastNodeRpt() {
		return nodeDao.getLastNodeRpt();
	}

	@Override
	public boolean updateNodeStatus(long nodeId, int notReporting) {
		return nodeDao.updateNodeStatus(nodeId,notReporting);
	}

	@Override
	public JNode getNode(long nodeId) {
		return nodeDao.getNode(nodeId);
	}

	@Override
	public boolean isNodeEnabled(long nodeId) {
		return nodeDao.isNodeEnabled(nodeId);
	}
	
}
