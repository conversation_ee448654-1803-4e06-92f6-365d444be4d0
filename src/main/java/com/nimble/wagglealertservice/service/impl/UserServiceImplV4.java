package com.nimble.wagglealertservice.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.wagglealertservice.dao.IUserDaoV4;
import com.nimble.wagglealertservice.dto.JUser;
import com.nimble.wagglealertservice.dto.UserV4;
import com.nimble.wagglealertservice.exception.InvalidAuthoException;
import com.nimble.wagglealertservice.service.IUserServiceV4;


@Service
@Transactional
public class UserServiceImplV4 implements IUserServiceV4{
	
	@Autowired
	IUserDaoV4 userDao;

	@Override
	public UserV4 verifyAuthV4(String key,String value) throws InvalidAuthoException {
		return userDao.verifyAuthV4(key,value);
	}

	@Override
	public long[] getCreditPoints() {
		return userDao.getCreditPoints();
	}

	@Override
	public long[] getNoOfEmailsAndMobilesConf(int companyId) {
		return userDao.getNoOfEmailsAndMobilesConf(companyId);
	}

	@Override
	public boolean getAppNotifyEnable(int companyId) {
		return userDao.getAppNotifyEnable(companyId);
	}

	@Override
	public List<String> getTokensByGatewayId(long gatewayId) {
		return userDao.getTokensByGatewayId(gatewayId);
	}

	@Override
	public int getCompanyType(long companyId) {
		return userDao.getCompanyType(companyId);
	}

	@Override
	public boolean isVoiceAlert(long alertCfgId, String startDateTime) {
		return userDao.isVoiceAlert(alertCfgId,startDateTime);
	}

	@Override
	public JUser getUserPlanVersion(long gatewayId) {
		return userDao.getUserPlanVersion(gatewayId);
	}
	
	@Override
	public String getAlertBasedon() {
		return userDao.getAlertBasedon();
	}

	@Override
	public String getTempUnitByCmpy(long cmp_id) {
		return userDao.getTempUnitByCmpy(cmp_id);
	}

	@Override
	public boolean checkFlexiPlanHistory(long gatewayId) {
		return userDao.checkFlexiPlanHistory(gatewayId);
	}
}
