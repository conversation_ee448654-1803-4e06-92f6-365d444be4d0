package com.nimble.wagglealertservice.service.impl;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.wagglealertservice.dao.IAlertDaoV4;
import com.nimble.wagglealertservice.dto.AlertCfgV4;
import com.nimble.wagglealertservice.dto.JAlertNotifyHistory;
import com.nimble.wagglealertservice.entity.AlertRangeNotification;
import com.nimble.wagglealertservice.pojo.JAlertCountReport;
import com.nimble.wagglealertservice.pojo.JTempRangeReport;
import com.nimble.wagglealertservice.service.IAlertServiceV4;

@Service
@Transactional
public class AlertServiceImplV4 implements IAlertServiceV4 {

	@Autowired
	@Lazy
	IAlertDaoV4 alertDaoV4;

	@Override
	public boolean ackExpiredAlerts(long alertid) {
		return alertDaoV4.ackExpiredAlerts(alertid);
	}

	@Override
	public ArrayList<JAlertNotifyHistory> getAlertnotifyhistorylist() {
		return alertDaoV4.getAlertnotifyhistorylist();
	}

	@Override
	public JAlertNotifyHistory getAlertnotifyhistory(long alertId) {
		return alertDaoV4.getAlertnotifyhistory(alertId);
	}

	@Override
	public boolean UpdateAlertnotifyHistoryNotify(int count, String currentTime, long id, long alertId, String smsMsg, String timeZone) {
		return alertDaoV4.UpdateAlertnotifyHistoryNotify(count, currentTime, id, alertId, smsMsg, timeZone);
	}

	@Override
	public boolean InsertAlertnotifyHistoryNotify(long alertId, long alertCfgId, String startDateTime, String endDateTime,
			int notifyFreq, String currentTime, String timeZone, int notifiedCount, String alertMsg) {
		return alertDaoV4.InsertAlertnotifyHistoryNotify( alertId,  alertCfgId,  startDateTime,  endDateTime,
				 notifyFreq,  currentTime,  timeZone,  notifiedCount,  alertMsg);
	}

	@Override
	public boolean UpdateAlert(long gatewayId) {
		return alertDaoV4.UpdateAlert(gatewayId);
	}

	@Override
	public boolean DeleteplAlertHistoryStatus(long alertId) {
		return alertDaoV4.DeleteplAlertHistoryStatus(alertId);
	}

	@Override
	public boolean InsertAlert(String startDateTime, String endDateTime, String timeZone, float alertValue, String unit,
			double lat, String latDir, double lon, String longDir, String address, String gpsstatus, float battStatus,
			int rawrssi, String rssi, float externalsensor, float humidity, float temperature, float light,
			String motion, float pressure, long alerttypeId, long assetId, long gatewayId, long alertCfgId,
			long companyId,String firstnotifiedtime, String lastnotifiedtime, String alertMsg,int notifyCount,
			int sms,int mail,int notify,float minval,float maxval,String notificationtype,String msgId) {
		return alertDaoV4.InsertAlert(startDateTime, endDateTime, timeZone, alertValue, unit, lat, latDir, lon, longDir,
				address, gpsstatus, battStatus, rawrssi, rssi, externalsensor, humidity, temperature, light, motion,
				pressure, alerttypeId, assetId, gatewayId, alertCfgId, companyId, firstnotifiedtime,  lastnotifiedtime,
				alertMsg, notifyCount, sms, mail,notify,minval,maxval,notificationtype,msgId);
	}

	@Override
	public long getAlertId(String startTime, long alertCfgId) {
		return alertDaoV4.getAlertId(startTime, alertCfgId);
	}

	@Override
	public boolean updateplByAlertId(long alertId, String schedulerName) {
		return alertDaoV4.updateplByAlertId(alertId, schedulerName);
	}

	@Override
	public boolean UpdateAlertNotified(long alertId, String notifiedTime, String alertMsg,int sms,int mail,int notify,String msgId) {
		return alertDaoV4.UpdateAlertNotified(alertId, notifiedTime, alertMsg, sms, mail, notify, msgId);
	}

	@Override
	public boolean insertAlertStatus(String operation, long cmp_id, long gatewayId, long alerttype_id,
			boolean alert_status) {
		return alertDaoV4.insertAlertStatus(operation, cmp_id, gatewayId, alerttype_id, alert_status);
	}

	@Override
	public boolean saveAlertRangeNotification(AlertRangeNotification arNotification) {
		return alertDaoV4.saveAlertRangeNotification(arNotification);
	}

	@Override
	public AlertRangeNotification getAlertRangeNotification(long user_id, long gatewayId, long alerttype_id) {
		return alertDaoV4.getAlertRangeNotification(user_id, gatewayId, alerttype_id);
	}
	
	@Override
	public AlertCfgV4 getAlertMinMax(long alertCfgId) {
		return alertDaoV4.getAlertMinMax(alertCfgId);
	}

	@Override
	public ArrayList<JTempRangeReport> getTempRangeReport(String date) {
		return alertDaoV4.getTempRangeReport(date);
	}

	@Override
	public ArrayList<JAlertCountReport> getAlertCountReport(String date) {
		return alertDaoV4.getAlertCountReport(date);
	}

}
