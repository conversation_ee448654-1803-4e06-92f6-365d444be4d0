package com.nimble.wagglealertservice.service.impl;

import java.util.ArrayList;

import com.nimble.wagglealertservice.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.wagglealertservice.dao.IDeviceStatusDao;
import com.nimble.wagglealertservice.service.IDeviceStatusService;

@Service
@Transactional
public class DeviceStatusServiceImpl implements IDeviceStatusService {

	@Autowired
	IDeviceStatusDao deviceDao;

	@Override
	@Transactional( propagation = Propagation.SUPPORTS,readOnly = true )
	public ArrayList<JLastGatewayRpt> getLastGatewayReport(int count) {
		return deviceDao.getLastGatewayReport(count);
	}
	
	@Override
	@Transactional
	public boolean isNRAlert(long companyId, String currentDateTime, long gatewayId) {
		return deviceDao.isNRAlert(companyId, currentDateTime, gatewayId);
	}

	@Override
	@Transactional
	public AlertCfgV4 getAlertCfg(long assetId, long companyId,long alerttype_id) {
		return deviceDao.getAlertCfg(assetId, companyId,alerttype_id);
	}

	@Override
	@Transactional
	public JAlert getAlert(long alertCfgId, long assetId) {
		return deviceDao.getAlert(alertCfgId, assetId);
	}

	@Override
	@Transactional
	public boolean updateAlert(long alertCfgId, String endDateTime, int count, long assetId) {
		return deviceDao.updateAlert(alertCfgId, endDateTime, count, assetId);
	}

	@Override
	@Transactional
	public boolean insertAlert(String startDateTime, String endDateTime, String timeZone, float alertValue, String unit,
			double lat, String latDir, double lon, String longDir, String address, String gpsstatus, float battStatus,
			int rawrssi, String rssi, float externalsensor, float humidity, float temperature, float light,
			String motion, float pressure, long alerttypeId, long assetId, long gatewayId, long nodeId, long alertCfgId,
			long companyId) {
		return deviceDao.insertAlert(startDateTime, endDateTime, timeZone, alertValue, unit, lat, latDir, lon, longDir,
				address, gpsstatus, battStatus, rawrssi, rssi, externalsensor, humidity, temperature, light, motion,
				pressure, alerttypeId, assetId, gatewayId, nodeId, alertCfgId, companyId);
	}

	@Override
	@Transactional
	public long getAlertId(String startDateTime, long alertCfgId) {
		return deviceDao.getAlertId(startDateTime, alertCfgId);
	}

	@Override
	@Transactional
	public long[] getCreditsInfoForGateway(int gatewayId, int companyId) {
		return deviceDao.getCreditsInfoForGateway(gatewayId, companyId);
	}

	@Override
	@Transactional
	public boolean updateThrottlingCount(long gatewayId, String gatewayname, long companyId, JGateway asset) {
		return deviceDao.updateThrottlingCount(gatewayId, gatewayname, companyId, asset);
	}

	@Override
	@Transactional
	public boolean updatealertnotifyhistory(String endDateTime, String timeZone, long alertId, int notifyFreq) {
		return deviceDao.updatealertnotifyhistory(endDateTime, timeZone, alertId, notifyFreq);
	}

	@Override
	@Transactional
	public boolean updateGatewayStatus1(long gatewayid, int notReporting) {
		return deviceDao.updateGatewayStatus1(gatewayid, notReporting);
	}

	@Override
	@Transactional
	public long getUserGateway(long gatewayId) {
		return deviceDao.getUserGateway(gatewayId);
	}

	@Override
	public boolean updateDailyAlertCount(JDailyAlertTypeCount jdatCount) {
		return deviceDao.updateDailyAlertCount(jdatCount);
	}
	
	@Override
	public boolean updateGatewayShowVideo(long gatewayid, boolean isShow) {
		return deviceDao.updateGatewayShowVideo( gatewayid, isShow);
	}

	@Override
	public long getMonitortype(long gatewayId) {
		return deviceDao.getMonitortype(gatewayId);
	}

    @Override
    public boolean checkLastCreditNotification(long gatewayId, String featurecodeSms,JUser juser) {
        return deviceDao.checkLastCreditNotification(gatewayId, featurecodeSms,juser);
    }

    @Override
    public long getAlertPushNotificationId(String featureCode) {
        return deviceDao.getAlertPushNotificationId(featureCode);
    }

	@Override
	public boolean checkIsThreadSensor(long gatewayId) {
		return deviceDao.checkIsThreadSensor(gatewayId);
	}

	@Override
	public long getHubIdForSensor(long nodeId) {
		return deviceDao.getHubIdForSensor(nodeId);
	}

}
