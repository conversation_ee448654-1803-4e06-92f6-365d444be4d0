package com.nimble.wagglealertservice.service.impl;

import org.springframework.beans.factory.annotation.Autowired ;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.wagglealertservice.dao.IScheduleAlertDao;
import com.nimble.wagglealertservice.entity.JScheduleAlert;
import com.nimble.wagglealertservice.service.IScheduleAlertService;

@Service
@Transactional
public class ScheduleAlertServiceImpl implements IScheduleAlertService {

	@Autowired
	IScheduleAlertDao scheduleDao;

	@Override
	public boolean createOrUpdateAlert(JScheduleAlert alert) {
		return scheduleDao.createOrUpdateAlert(alert);
	}
	
	@Override
	public boolean updateAlertByName(String name, int status) {
		return scheduleDao.updateAlertByName(name,status);
	}

	@Override
	public JScheduleAlert getScheduleAlertByName(String name) {
		return scheduleDao.getScheduleAlertByName(name);
	}

	@Override
	public boolean updateScheduleAlert(long alertId, String schedulerName) {
		return scheduleDao.updateScheduleAlert(alertId, schedulerName);
	}

}
