package com.nimble.wagglealertservice.service.impl;

import java.text.SimpleDateFormat;
import java.util.Calendar;

import javax.transaction.Transactional;

import org.apache.http.entity.StringEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.nimble.wagglealertservice.dto.JSendNotification;
import com.nimble.wagglealertservice.dto.JUser;
import com.nimble.wagglealertservice.dto.UserV4;
import com.nimble.wagglealertservice.entity.AlertRangeNotification;
import com.nimble.wagglealertservice.entity.JScheduleAlert;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IAsyncService;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.IScheduleAlertService;
import com.nimble.wagglealertservice.service.IUserServiceV4;
import com.nimble.wagglealertservice.service.SMSInterface;

@Service
@Transactional
public class AsyncServiceImpl implements IAsyncService {
	private static final Logger log = LogManager.getLogger(AsyncServiceImpl.class);
	@Autowired
	IUserServiceV4 userserviceV4;

	@Autowired
	IAlertServiceV4 alertServiceV4;

	@Autowired
	IDeviceStatusService deviceStatusService;

	@Autowired
	IScheduleAlertService scheduleAlert;

	public AsyncServiceImpl() {
	}

	@Override
	@Async("asyncExecutor")
	public void sendMarketingNotification(JScheduleAlert jAlert, String server_ip, String validate_auth,
			boolean push_notification, boolean email_notification, int alert_pushNotificationId,
			String remarks,String tempunit,int alert_check_range) {
		try {
			boolean sendPN = false;
			boolean sendMail = false;
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Calendar cal = Calendar.getInstance(); // get current utc
			
			JUser juser = userserviceV4.getUserPlanVersion(jAlert.getGateway_id());
			jAlert.setUser_id(juser.getUser_id());
			
			AlertRangeNotification arnObj = alertServiceV4.getAlertRangeNotification(jAlert.getUser_id(),
					jAlert.getGateway_id(), jAlert.getAlerttype_id());

			if (arnObj != null) {
				Calendar mailCal = Calendar.getInstance(); // get current utc
				Calendar pnCal = Calendar.getInstance(); // get current utc

				mailCal.setTime(sdf.parse(arnObj.getMail_datetime()));
				pnCal.setTime(sdf.parse(arnObj.getPn_datetime()));

				long difference = cal.getTimeInMillis() - mailCal.getTimeInMillis();
				long hrsBetween = (difference / (1000 * 60 * 60));

				if (hrsBetween >= 24)
					sendMail = true;

				difference = cal.getTimeInMillis() - pnCal.getTimeInMillis();
				hrsBetween = (difference / (1000 * 60 * 60));

				if (hrsBetween >= 24)
					sendPN = true;

			} else {
				sendPN = true;
				sendMail = true;
			}

			SMSInterface smsGateway = new Plivo();
			int temp_range = alert_check_range;
			
			if(tempunit.equalsIgnoreCase("C"))
				temp_range = Math.round((float)(alert_check_range)/1.8f);
			//Your current min/max limits are too close to the ambient value. Please set it the limits +/- 5 Deg C/F above/below the ambient temp to avoid false alerts.
			String emailMsg = remarks+"\nYour current min/max limits are too close to the ambient value. Please set it the limits +/- "+temp_range+" Deg "+tempunit+" above/below the ambient temp to avoid false alerts.";
			String emailSub = "Alert is so near! RESET now";
			boolean sentEmailMsg = false;
			boolean sentAppNotify = false;
			//String app_msg = jAlert.getApp_msg();

			if (sendMail) {
				log.info("Mail Notification:Gatewayid: " + jAlert.getGateway_id());
				sentEmailMsg = smsGateway.callIrisEmailAPI(jAlert.getEmail(), emailSub, emailMsg, server_ip, true,validate_auth);
			}

			if (sendPN) {
				log.info("App Notification:Gatewayid: " + jAlert.getGateway_id());
				try {
					long[] userID = new long[1];
					userID[0] =jAlert.getUser_id();
					
					JSendNotification jsn = new JSendNotification(alert_pushNotificationId, userID);
					String url = server_ip + "/irisservices/v3.0/sendnotifications/"+juser.getAuthkey();
					Gson gson = new Gson();
					String json = gson.toJson(jsn);
					StringEntity postingString = new StringEntity(json);
					
					sentAppNotify = smsGateway.postAPIWithAuthHeader(url, null, postingString);
					log.info("sendnotifications : " + sentAppNotify);
				}catch (Exception e) {
					log.error("Error sendnotifications:"+e.getLocalizedMessage());
				}

			}
			String utc = sdf.format(cal.getTime());
			arnObj = new AlertRangeNotification(jAlert.getUser_id(), jAlert.getGateway_id(), jAlert.getCmp_id(), utc,
					jAlert.getAlerttype_id(), jAlert.getAlertvalue());

			arnObj.setRemarks(emailMsg);

			if (sentEmailMsg || sentAppNotify) {

				if (sentEmailMsg) {
					arnObj.setMail_content(emailMsg);
					arnObj.setMail_datetime(utc);
				}

				if (sentAppNotify) {
					arnObj.setPn_content(emailSub);
					arnObj.setPn_datetime(utc);
				}

			}
			alertServiceV4.saveAlertRangeNotification(arnObj);

		} catch (Exception e) {
			log.error("sendMarketingNotification: " + e.getLocalizedMessage());
		}
	}

	@Override
	@Async("asyncExecutor")
	public void saveMarketingNotification(JScheduleAlert jAlert,  String remarks) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Calendar cal = Calendar.getInstance(); // get current utc

			JUser juser = userserviceV4.getUserPlanVersion(jAlert.getGateway_id());
			jAlert.setUser_id(juser.getUser_id());
			String utc = sdf.format(cal.getTime());
			AlertRangeNotification arnObj = new AlertRangeNotification(jAlert.getUser_id(), jAlert.getGateway_id(),
					jAlert.getCmp_id(), utc, jAlert.getAlerttype_id(), jAlert.getAlertvalue());

			arnObj.setRemarks(remarks);

			alertServiceV4.saveAlertRangeNotification(arnObj);

		} catch (Exception e) {
			log.error("saveMarketingNotification: " + e.getLocalizedMessage());
		}
	}

	@Override
	public void updateGatewayShowVideo(long gatewayid, boolean isShow) {
		deviceStatusService.updateGatewayShowVideo(gatewayid, isShow);
		
	}

    @Override
    @Async("asyncExecutor")
    public void sendLastCreditNotification(JScheduleAlert jAlert, String server_ip, String validateAuth, boolean pushNotification) {
        try {
            log.info("sendLastCreditNotification begins...");
            JUser juser = userserviceV4.getUserPlanVersion(jAlert.getGateway_id());
            jAlert.setUser_id(juser.getUser_id());
            boolean sendPN = false;
            boolean sentAppNotify = false;
            SMSInterface smsGateway = new Plivo();
            sendPN = deviceStatusService.checkLastCreditNotification(jAlert.getGateway_id(),jAlert.getFeaturecode_SMS(), juser);
            log.info("sendPN for last credit check: {}", sendPN);
            if (sendPN) {
                log.info(" Gatewayid: For last credit check {} ", jAlert.getGateway_id());
                try {
                    long[] userID = new long[1];
                    userID[0] = jAlert.getUser_id();
                    long pushNotificationId = deviceStatusService.getAlertPushNotificationId(jAlert.getFeaturecode_SMS());

                    if (pushNotificationId != 0) {
                        JSendNotification jsn = new JSendNotification((int) pushNotificationId, userID);
                        String url = server_ip + "/irisservices/v3.0/sendnotifications/" + juser.getAuthkey()+"?isAlertExhausted=true";
                        Gson gson = new Gson();
                        String json = gson.toJson(jsn);
                        StringEntity postingString = new StringEntity(json);
                        sentAppNotify = smsGateway.postAPIWithAuthHeader(url, null, postingString);
                    }
                    log.info("sendnotifications : {}", sentAppNotify);
                } catch (Exception e) {
                    log.error("Error sendnotifications: {}", e.getLocalizedMessage());
                }

            }
        } catch (Exception e) {
            log.error("sendLastCreditNotification: {}", e.getLocalizedMessage());
        }
    }

}
