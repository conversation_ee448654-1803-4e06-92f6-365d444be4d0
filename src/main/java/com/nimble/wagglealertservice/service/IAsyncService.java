package com.nimble.wagglealertservice.service;

import com.nimble.wagglealertservice.entity.JScheduleAlert;

public interface IAsyncService {

	
	public void sendMarketingNotification(JScheduleAlert jAlert,  String server_ip, String validate_auth,
			boolean push_notification,boolean email_notification, int alert_pushNotificationId,
			String remarks,String tempunit,int alert_check_range);
	
	public void saveMarketingNotification(JScheduleAlert jAlert, String remarks);
	
	public void updateGatewayShowVideo(long gatewayid, boolean isShow);

    void sendLastCreditNotification(JScheduleAlert alert, String trim, String validateAuth, boolean pushNotification);
}
