package com.nimble.wagglealertservice.dao.impl;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.wagglealertservice.dao.ICheckserviceDao;
import com.nimble.wagglealertservice.entity.Testtable;

@Repository
public class CheckserviceDaoImpl implements ICheckserviceDao{
	private static final Logger log = LogManager.getLogger(CheckserviceDaoImpl.class);

	@Autowired
	private SessionFactory sessionFactory;

	@Override
	public Testtable DBSaveOrUpdatequery(Testtable testtable) {
		log.info(" Entering DBSaveOrUpdatequery :: DAO ");
		try {
			Session session = sessionFactory.getCurrentSession();
			session.merge(testtable);
			return testtable;
		} catch (Exception e) {
			log.error(e.getMessage());
		}
		return null;
	}

	@Override
	public Testtable DBDeletequery(Testtable testtable) {
		log.info(" Entering DBDeleteQuery :: DAO ");
		try {
			Session session = sessionFactory.getCurrentSession();
			session.delete(testtable);
			return testtable;
		} catch (Exception e) {
			log.error(e.getMessage());
		}
		return null;
	}

	@Override
	public Testtable DBselectquery(Testtable testtable) {
		log.info(" Entering DBselectquery :: DAO ");
		try {
			Session session = sessionFactory.getCurrentSession();
			testtable=(Testtable) session.createQuery("from Testtable where check_service='"+testtable.getCheckServer()+"' and  server_ip = '"+testtable.getServerIp()+"'").list().get(0);
			return testtable;
		} catch (Exception e) {
			log.error(e.getMessage());
		}
		return null;
	}



}
