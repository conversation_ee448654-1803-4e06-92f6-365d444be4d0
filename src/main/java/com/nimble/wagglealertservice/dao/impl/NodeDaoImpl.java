package com.nimble.wagglealertservice.dao.impl;

import java.math.BigInteger;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.wagglealertservice.dao.INodeDao;
import com.nimble.wagglealertservice.dto.JLastNodeRpt;
import com.nimble.wagglealertservice.dto.JNode;

@Repository
public class NodeDaoImpl implements INodeDao{
	
	private static final Logger log = LogManager.getLogger(NodeDaoImpl.class);
	
	@Autowired
	private SessionFactory sessionFactory;

	@Override
	public ArrayList<JLastNodeRpt> getLastNodeRpt() {
		ArrayList<JLastNodeRpt> nodeList = new ArrayList<JLastNodeRpt>();
		try {
			 Session session = sessionFactory.getCurrentSession();
			 String qry = "SELECT `date`,`time`,`node_id`,`timezone` FROM `lastnodereport`;";
			 List<Object[]> objList = (List<Object[]>) session.createSQLQuery(qry).list();
			 
			 for(Object[] obj : objList) {
				 JLastNodeRpt lnr = new JLastNodeRpt();
				 Date date = (Date) obj[0];
				 Time time = (Time) obj[1];
				 long nodeId = ((BigInteger) obj[2]).longValue();
				 String timezone = (String) obj[3];
				 
				 lnr.setDate(date);
				 lnr.setTime(time);
				 lnr.setNode_id(nodeId);
				 lnr.setTimezone(timezone);
				 
				 nodeList.add(lnr);
			}
			 
			 if(objList == null || objList.isEmpty())
				 return null;
		}catch (Exception e) {
			log.info("getLastNodeRpt : "+e.getLocalizedMessage());
			return null;
		}
		return nodeList;
	}

	@Override
	public boolean updateNodeStatus(long nodeId, int status) {
		
		try {
			String query = "update `node` set `isalive`=" + status + " where `id`='" + nodeId + "';";
			Session session = sessionFactory.getCurrentSession();
			
			int updateCount = session.createSQLQuery(query).executeUpdate();
			if (updateCount == 0)
				return false;
			else
				return true;
			
		}catch (Exception e) {
			log.info("getLastNodeRpt : "+e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public JNode getNode(long nodeId) {
		
		JNode node = new JNode();
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT N.`cmp_id`,N.`gateway_id`,N.`name`,G.`name` AS gateway_name FROM `node` AS N JOIN `gateway` AS G ON N.`gateway_id`=G.`id` WHERE N.`id`= "+ nodeId;
			
			Object[] obj = (Object[]) session.createSQLQuery(qry).list().get(0);
			
			long cmp_id = ((BigInteger) obj[0]).longValue();
			long gateway_id = ((BigInteger) obj[1]).longValue();
			String name = (String) obj[2];
			String gatewayName = (String) obj[3];
			
			node.setCmp_id(cmp_id);
			node.setGatewayid(gateway_id);
			node.setName(name);
			node.setGatewayname(gatewayName);
			
		}catch (Exception e) {
			log.info("getLastNodeRpt : "+e.getLocalizedMessage());
			return null;
		}
		return node;
	}

	@Override
	public boolean isNodeEnabled(long nodeId) {
		
        boolean retValue = false;
        try {
        	String qry = "select * from `node` where `id`='"+ nodeId +"' and `isenable`=1;";
        	Session session = sessionFactory.getCurrentSession();
        	Object obj = (Object) session.createSQLQuery(qry).list().get(0);
        	if(obj != null)
                retValue = true;
        	else 
        		retValue = false;
        } 
        catch (Exception e) {
            log.error(nodeId + " : " + "IsSiteEnabled: "+e);
        }
       return retValue;
	}	
	
}
