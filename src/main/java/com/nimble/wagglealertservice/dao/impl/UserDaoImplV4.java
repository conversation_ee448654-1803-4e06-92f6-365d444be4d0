package com.nimble.wagglealertservice.dao.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.wagglealertservice.dao.IUserDaoV4;
import com.nimble.wagglealertservice.dto.JUser;
import com.nimble.wagglealertservice.dto.UserV4;
import com.nimble.wagglealertservice.exception.InvalidAuthoException;

@Repository
public class UserDaoImplV4 implements IUserDaoV4 {

	@Autowired
	private SessionFactory sessionFactory;

	private static final Logger log = LogManager.getLogger(UserDaoImplV4.class);

	@Override
	public UserV4 verifyAuthV4(String key, String value) throws InvalidAuthoException {

		log.info("Entered :: UserDaoImplV4 :: verifyAuthV4:: ");
		UserV4 usr = null;
		try {

			Query qry = sessionFactory.getCurrentSession().createSQLQuery(
					"SELECT U.id, U.cmp_id,U.chargebeeid , U.email,U.authkey FROM user U  WHERE U." + key + "='" + value + "';");

			List res = qry.list();

			if (!res.isEmpty() && res.size() > 0) {

				Object[] tuple = (Object[]) res.get(0);

				usr = new UserV4();

				if (tuple[0] != null) {
					BigInteger userId = (BigInteger) tuple[0];
					usr.setId(userId.longValue());
				}
				if (tuple[1] != null) {
					BigInteger cmp_id = (BigInteger) tuple[1];
					usr.setCmpId(cmp_id.longValue());
				}
				if (tuple[2] != null)
					usr.setChargebeeid((String) tuple[2]);

				if (tuple[3] != null)
					usr.setEmail((String) tuple[3]);
				
				if (tuple[4] != null)
					usr.setAuthKey((String) tuple[4]);

				return usr;

			} else {
				throw new InvalidAuthoException();
			}
		} catch (Exception e) {
			log.error("Invalid auth exception : " + e.getLocalizedMessage());
			throw new InvalidAuthoException();
		}
	}
	
	@Override
	public JUser getUserPlanVersion(long gatewayId) {
		JUser user = null;
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT U.id, U.plan_ver,U.authkey  FROM  `user` U JOIN usergateway UG ON  UG.userid = U.id  WHERE UG.gatewayid=" + gatewayId;

			List<Object[]> list = session.createSQLQuery(qry).list();
			
			if (list != null && !list.isEmpty()) {
				Object[] userObj = list.get(0);
				long id  = ((BigInteger)userObj[0]).longValue();
				String version = userObj[1].toString();
				String authkey = userObj[2].toString();
				user = new JUser(id,version);
				user.setAuthkey(authkey);
			}
		} catch (Exception e) {
			log.error("getUserPlanVersion: "+e.getLocalizedMessage());
		} 		
		return user;
	}
	
	@Override
	public String getAlertBasedon(){
		String alertcount_basedon = "alert-based";
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT `value` FROM external_config WHERE  parametername = 'alertcount'" ;

			List res = session.createSQLQuery(qry).list();

			if (res != null && !res.isEmpty()) {
				alertcount_basedon = res.get(0).toString();
			}
		} catch (Exception e) {
			log.error("getAlertBasedon: "+e.getLocalizedMessage());
		} 		
		return alertcount_basedon;
	}
	
	@Override
	public long[] getCreditPoints() {
		log.info("Entered :: NotifyAlertServiceDaoImplV4 :: getCreditPoints::  ");
		long credit_points[] = new long[4];
		try {
			Session ses = sessionFactory.getCurrentSession();
			String Qry = "SELECT `credits` FROM `credit_points` WHERE `id` IN(2,3,4,5) ORDER BY `id` ASC";

			List res = ses.createSQLQuery(Qry).list();

			log.info("Qry : " + Qry);
			

			if (!res.isEmpty()) {
				credit_points[0] = ((BigInteger) res.get(0)).longValue();
				credit_points[1] = ((BigInteger) res.get(1)).longValue();
				credit_points[2] = ((BigInteger) res.get(2)).longValue();
				credit_points[3] = ((BigInteger) res.get(3)).longValue();
				return credit_points;
			} else {
				return credit_points;
			}

		} catch (Exception e) {
			
			log.error("getCreditPoints : " + e.getLocalizedMessage());
			return credit_points;
		}
	}

	@Override
	public long[] getNoOfEmailsAndMobilesConf(int companyId) {
		log.info("Entered :: NotifyAlertServiceDaoImplV4 :: getCreditPoints::  ");
		long nos[] = new long[2];
		try {
			Session ses = sessionFactory.getCurrentSession();
			String Qry = "select a.mobileNos as noofMobiles,a.emailIds as noofEmails from throttlingsettings a,"
					+ "company b where a.id = b.throttsettings_id and b.id='" + companyId + "';";
			List<Object[]> res = ses.createSQLQuery(Qry).list();

			log.info("Qry : " + Qry);

			if (!res.isEmpty()) {
				Object[] obj = (Object[]) res.get(0);
				nos[0] = ((BigInteger) obj[0]).longValue();
				nos[1] = ((BigInteger) obj[1]).longValue();

				return nos;
			} else {
				return nos;
			}

		} catch (Exception e) {
			
			log.error("getCreditPoints : " + e.getLocalizedMessage());
			return nos;
		}
	}

	@Override
	public boolean getAppNotifyEnable(int companyId) {
		log.info("Entered :: NotifyAlertServiceDaoImplV4 :: getAppNotifyEnable::  ");
		try {
			Session ses = sessionFactory.getCurrentSession();
			String Qry = "SELECT `appnotifyenable` FROM `companyconfig` WHERE " + "`cmp_id`='" + companyId + "'";

			List res = ses.createSQLQuery(Qry).list();

			log.info("Qry : " + Qry);

			if (!res.isEmpty()) {
				boolean appNotify = (boolean) res.get(0);
				return appNotify;
			} else {
				return false;
			}

		} catch (Exception e) {
			
			log.error("getAppNotifyEnable : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public List<String> getTokensByGatewayId(long gatewayId) {
		log.info("Entered :: NotifyAlertServiceDaoImplV4 :: getAppNotifyEnable::  ");

		List<String> tokens = new ArrayList<String>();
		try {
			Session ses = sessionFactory.getCurrentSession();
			String Qry = "SELECT token FROM usertoken AS UT LEFT JOIN usergateway AS UG ON UG.userId = UT.userId LEFT JOIN user AS U "
					+ "ON U.id = UT.userId WHERE UG.gatewayId = " + gatewayId
					+ " AND UT.enable = '1' ORDER BY U.role_id DESC";

			List<String> res = ses.createSQLQuery(Qry).list();
			if (res != null && !res.isEmpty()) {

				for (String obj : res) {

					String id = (String) obj;

					tokens.add(id);
				}
				return tokens;
			}else {
				return null;
			}
			
		} catch (Exception e) {
			
			log.error("getAppNotifyEnable : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public int getCompanyType(long companyId) {
		log.info("Entered :: NotifyAlertServiceDaoImplV4 :: getCompanyType::  ");
		try {
			Session ses = sessionFactory.getCurrentSession();
			String Qry = "select `cmptype_id` from `company` where `id`='" + companyId + "';";

			List res = ses.createSQLQuery(Qry).list();

			log.info("Qry : " + Qry);

			if (!res.isEmpty()) {
				int id = ((BigInteger) res.get(0)).intValue();
				return id;
			} else {
				return -1;
			}

		} catch (Exception e) {
			log.error("getCompanyType : " + e.getLocalizedMessage());
			return 0;
		}
	}

	@Override
	public boolean isVoiceAlert(long alertCfgId, String startDateTime) {
		log.info("Entered :: NotifyAlertServiceDaoImplV4 :: getAppNotifyEnable::  ");
		try {
			Session ses = sessionFactory.getCurrentSession();
			String Qry = "SELECT `voicealertStarttime`,`voicealertstop` FROM `alertcfg` WHERE (`id` = '" + alertCfgId
					+ "') " + "AND ((`voicealertStarttime` <= `voicealertstop` AND  (TIME('" + startDateTime
					+ "')>=`voicealertStarttime` AND  TIME('" + startDateTime + "')<=`voicealertstop`)) "
					+ "OR (`voicealertstop` < `voicealertStarttime` AND (TIME('" + startDateTime
					+ "') <=`voicealertstop` OR TIME('" + startDateTime + "')>=`voicealertStarttime`)));";

			List res = ses.createSQLQuery(Qry).list();

			log.info("Qry : " + Qry);

			if (!res.isEmpty()) {
				return true;
			} else {
				return false;
			}

		} catch (Exception e) {
			
			log.error("getAppNotifyEnable : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public String getTempUnitByCmpy(long cmp_id) {
		String unit = ""; 
		try {
			String qry = "SELECT temperatureunit FROM companyconfig WHERE cmp_id ='" + cmp_id+ "';";
	
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
				unit = ((String) res.get(0));
			} 
		}catch (Exception e) {
			log.error("get TempUnitByCmpy : ",e.getLocalizedMessage());
		}
		return unit.trim();
	}

	@Override
	public boolean checkFlexiPlanHistory(long gatewayId) {
		log.info("Entered checkFlexiPlanHistory : "+gatewayId);
		try {
			String qry = "SELECT aps.id,fph.gateway_id " +
					"FROM `all_product_subscription` aps " +
					"LEFT JOIN `flexi_plan_history` fph ON aps.gateway_id=fph.gateway_id " +
					"WHERE aps.gateway_id="+gatewayId+ " " +
					"AND aps.plan_id like '%flexi%' ORDER BY updated_indb DESC LIMIT 1;";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();
			if (!res.isEmpty()) {
				Object[] obj = res.get(0);
				if(obj[0] != null && obj[1] == null) {
					return false;
				}
			}
		}catch (Exception e) {
			log.error("Error on checkFlexiPlanHistory : "+e.getLocalizedMessage());
		}
		return true;
	}

}
