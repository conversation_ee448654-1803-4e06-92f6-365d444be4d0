package com.nimble.wagglealertservice.dao.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import com.nimble.wagglealertservice.dao.IAlertDaoV4;
import com.nimble.wagglealertservice.dto.AlertCfgV4;
import com.nimble.wagglealertservice.dto.JAlertNotifyHistory;
import com.nimble.wagglealertservice.entity.Alert;
import com.nimble.wagglealertservice.entity.AlertRangeNotification;
import com.nimble.wagglealertservice.pojo.JAlertCountReport;
import com.nimble.wagglealertservice.pojo.JTempRangeReport;

@Repository
public class AlertDaoImplV4 implements IAlertDaoV4 {

	private static final Logger log = LogManager.getLogger(AlertDaoImplV4.class);

	@Autowired
	@Lazy
	private SessionFactory sessionFactory;

	@Override
	public boolean ackExpiredAlerts(long alertId) {
		log.info("Entered :: AlertNotifyHistoryDaoImplV4 :: ackExpiredAlerts::  ");

		try {
			Session ses = sessionFactory.getCurrentSession();

			String updateQry = "UPDATE `alert` SET `ack`=1 WHERE ";

			if (alertId == 0)
				updateQry += "`endDateTime`< CONVERT_TZ(DATE_SUB(UTC_TIMESTAMP, INTERVAL 45 MINUTE),'+00:00',`timeZone`) ";

			else
				updateQry += " `id`=" + alertId + " ";

			updateQry += " AND `ack`=0; ";

			log.info(" Update Query : " + updateQry);
			int updateCount = ses.createSQLQuery(updateQry).executeUpdate();

			if (updateCount == 0)
				return false;
			else
				return true;

		} catch (Exception e) {
			log.error("ackExpiredAlerts : " + e.getLocalizedMessage());

		}
		return false;
	}

	@Override
	public ArrayList<JAlertNotifyHistory> getAlertnotifyhistorylist() {
		log.info("Entered :: AlertNotifyHistoryDaoImplV4 :: getAlertnotifyhistory::  ");

		ArrayList<JAlertNotifyHistory> aNotifyHistoryList = new ArrayList<JAlertNotifyHistory>();

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			Session session = sessionFactory.getCurrentSession();

			String qry = "SELECT `id`,`alertId`,`contactMgrId`,`timeZone`"
					+ ",`startDateTime`,`endDateTime`,`notifyFreq`,`notifiedCount`,`acknowledge`"
					+ ",`smsAlertMsg`,`lastNotifiedTime` FROM `alertnotifyhistory` WHERE `acknowledge`=0;";

			List<Object[]> list = session.createSQLQuery(qry).list();

			for (Object[] obj : list) {

				JAlertNotifyHistory aNH = new JAlertNotifyHistory();

				int obj1 = (Integer) obj[0];
				int obj2 = (Integer) obj[1];
				int obj3 = (Integer) obj[2];
				int obj4 = (Integer) obj[6];
				int obj5 = (Integer) obj[7];
				if (obj[0] != null) {
					Long l = new Long(obj1);
					aNH.setId(l);
				}
				if (obj[1] != null) {
					Long l = new Long(obj2);
					aNH.setAlertId(l);
				}
				if (obj[2] != null) {
					Long l = new Long(obj3);
					aNH.setContactMgrId(l);
				}
				if (obj[3] != null)
					aNH.setTimeZone((String) obj[3]);
				if (obj[4] != null)
					aNH.setStartDateTime(sdf.format(new Date(((Timestamp) obj[4]).getTime())));
				if (obj[5] != null)
					aNH.setEndDateTime(sdf.format(new Date(((Timestamp) obj[5]).getTime())));
				if (obj[6] != null)
					aNH.setNotifyFreqInSec(obj4);
				if (obj[7] != null)
					aNH.setNotifiedCount(obj5);
				if (obj[8] != null)
					aNH.setAcknowledge((boolean) obj[8]);
				if (obj[9] != null)
					aNH.setSmsAlertMsg((String) obj[9]);
				if (obj[10] != null)
					aNH.setLastNotifiedTime(sdf.format(new Date(((Timestamp) obj[10]).getTime())));

				aNotifyHistoryList.add(aNH);

			}

			return aNotifyHistoryList;
		} catch (ArrayIndexOutOfBoundsException e) {
			log.error("Exception ArrayIndexOutOfBoundsException occured getAlertnotifyhistory : "
					+ e.getLocalizedMessage());
			return null;
		} catch (Exception e) {
			log.error("Exception occured getAlertnotifyhistory : " + e.getLocalizedMessage());
			log.error(e.getLocalizedMessage());
			return null;
		}

	}

	@Override
	public JAlertNotifyHistory getAlertnotifyhistory(long alertId) {
		log.info("Entered :: AlertNotifyHistoryDaoImplV4 :: getAlertnotifyhistory::  ");

		JAlertNotifyHistory aNH = new JAlertNotifyHistory();

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			Session session = sessionFactory.getCurrentSession();

			String qry = "SELECT AH.`id`,AH.`contactMgrId`,AH.`timeZone`, AH.startDateTime,AH.endDateTime,AG.notifyfreq,"
					+ "AH.notifiedCount,AH.acknowledge,AH.smsAlertMsg,AH.lastNotifiedTime FROM `alertnotifyhistory` AH JOIN alert A ON A.id=AH.alertId"
					+ " JOIN alertcfg AG ON AG.id=A.alertcfg_id  WHERE AH.`alertId`=" + alertId
					+ " AND `acknowledge`=0;";

			List<Object[]> list = session.createSQLQuery(qry).list();

			if (list != null && !list.isEmpty()) {
				Object[] obj = list.get(0);

				int obj1 = (Integer) obj[0];
				int obj2 = (Integer) obj[1];
				String obj3 = (String) obj[2];
				int obj4 = (Integer) obj[5];
				int obj5 = (Integer) obj[6];
				if (obj[0] != null) {
					Long l = new Long(obj1);
					aNH.setId(l);
				}
				if (obj[1] != null) {
					Long l = new Long(obj2);
					aNH.setContactMgrId(l);
				}
				if (obj[2] != null)
					aNH.setTimeZone(obj3);
				if (obj[3] != null)
					aNH.setStartDateTime(sdf.format(new Date(((Timestamp) obj[3]).getTime())));
				if (obj[4] != null)
					aNH.setEndDateTime(sdf.format(new Date(((Timestamp) obj[4]).getTime())));
				if (obj[5] != null)
					aNH.setNotifyFreqInSec(obj4);
				if (obj[6] != null)
					aNH.setNotifiedCount(obj5);
				if (obj[7] != null)
					aNH.setAcknowledge((boolean) obj[7]);
				if (obj[8] != null)
					aNH.setSmsAlertMsg((String) obj[8]);
				if (obj[9] != null)
					aNH.setLastNotifiedTime(sdf.format(new Date(((Timestamp) obj[9]).getTime())));

				return aNH;
			} else {
				return null;
			}
		} catch (ArrayIndexOutOfBoundsException e) {
			log.error("Exception ArrayIndexOutOfBoundsException occured getAlertnotifyhistory : "
					+ e.getLocalizedMessage());
			return null;
		} catch (Exception e) {
			log.error("Exception occured getAlertnotifyhistory : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean UpdateAlertnotifyHistoryNotify(int count, String currentTime, long id, long alertId, String alertMsg,
			String timeZone) {
		log.info("Entered :: UpdateAlertnotifyHistoryNotify::  ");

		try {
			Session ses = sessionFactory.getCurrentSession();

			String updateQry = "update `alertnotifyhistory` set `notifiedCount`=" + (++count) + ", `lastNotifiedTime`='"
					+ currentTime + "', timeZone='" + timeZone + "', smsAlertMsg='" + alertMsg + "' where `id`=" + id
					+ " and `alertId`=" + alertId + " and `acknowledge`=0;";

			int updateCount = ses.createSQLQuery(updateQry).executeUpdate();
			log.info("updateQry : " + updateQry);
			if (updateCount > 0)
				return true;
			else
				return false;
		} catch (Exception e) {
			log.error("UpdateAlertnotifyHistoryNotify : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean InsertAlertnotifyHistoryNotify(long alertId, long alertCfgId, String startDateTime,
			String endDateTime, int notifyFreq, String currentTime, String timeZone, int notifiedCount,
			String alertMsg) {
		log.info("Entered :: UpdateAlertnotifyHistoryNotify  ");

		try {
			Session ses = sessionFactory.getCurrentSession();
			int insertResultCount = 0;
			try {

				String insertQry = "insert into `alertnotifyhistory`(`alertId`,`contactMgrId`,`startDateTime`"
						+ ",`endDateTime`,`notifyFreq`,`notifiedCount`,`acknowledge`,"
						+ "`smsAlertMsg`,`firstNotifiedTime`,`lastNotifiedTime`,`timeZone`)" + " values('" + alertId
						+ "','" + alertCfgId + "','" + startDateTime + "','" + endDateTime + "','" + notifyFreq + "',"
						+ "'" + notifiedCount + "','0','" + alertMsg + "','" + currentTime + "','" + currentTime + "','"
						+ timeZone + "')";

				insertResultCount = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("insertQry : " + insertQry);
			} catch (Exception e) {
				log.error("Error While Insert UpdateAlertnotifyHistoryNotify Details " + e.getMessage());
			}
			if (insertResultCount > 0)
				return true;
			else
				return false;

		} catch (Exception e) {
			log.error("UpdateAlertnotifyHistoryNotify : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean UpdateAlert(long gatewayId) {
		log.info("Entered :: AlertNotifyHistoryDaoImplV4 :: UpdateAlert::  ");

		try {
			Session ses = sessionFactory.getCurrentSession();

			String updateQry = "UPDATE `alert` SET `ack`=1 WHERE gateway_id=" + gatewayId
					+ " AND `ack`=0 AND alerttype_id IN('3','17')";

			log.info(" Update Query : " + updateQry);
			int updateCount = ses.createSQLQuery(updateQry).executeUpdate();

			if (updateCount == 0)
				return false;
			else
				return true;

		} catch (Exception e) {
			log.error("UpdateAlert : " + e.getLocalizedMessage());

		}
		return false;
	}

	@Override
	public boolean DeleteplAlertHistoryStatus(long alertId) {
		log.info("Entered :: AlertNotifyHistoryDaoImplV4 :: DeleteplAlertHistoryStatus::  ");

		try {
			Session ses = sessionFactory.getCurrentSession();

			String updateQry = "UPDATE `pl_alert_history` SET `deleted`=1 WHERE alert_id=" + alertId;

			log.info(" Update Query : " + updateQry);
			int updateCount = ses.createSQLQuery(updateQry).executeUpdate();

			if (updateCount == 0)
				return false;
			else
				return true;

		} catch (Exception e) {
			log.error("DeleteplAlertHistoryStatus : " + e.getLocalizedMessage());

		}
		return false;
	}

	@Override
	public boolean InsertAlert(String startDateTime, String endDateTime, String timeZone, float alertValue, String unit,
			double lat, String latDir, double lon, String longDir, String address, String gpsstatus, float battStatus,
			int rawrssi, String rssi, float externalsensor, float humidity, float temperature, float light,
			String motion, float pressure, long alerttypeId, long assetId, long gatewayId, long alertCfgId,
			long companyId,String firstnotifiedtime, String lastnotifiedtime, String alertMsg,int notifyCount,
			int sms,int mail,int notify,float minval,float maxval,String notificationtype,String msgId) {
		log.info("Entered into AlertNotifyHistoryDaoImplV4 :: InsertAlert");
		Session session = sessionFactory.getCurrentSession();
		int updateCount = 0;

		try {
			
			String qry = "insert into `alert`(`startdatetime`,`enddatetime`,`alertvalue`,`count`, `lat`,`latDir`,`lon`,`lonDir`,"
					+ "`address`,`gpsstatus`,`battery`,`rawrssi`,`rssi`,`externalsensor`,`humidity`,`temperature`,`light`,`motion`,"
					+ "`pressure`,`alerttype_id`,`gateway_id`,`asset_id`,`alertcfg_id`,`cmp_id`,`timeZone`,firstnotifiedtime,"
					+ "lastnotifiedtime,notified_count,sms_ncount,mail_ncount,notify_ncount,minval,maxval,first_ntype,cur_ntype,msg_id) values('"
					+ startDateTime + "','" + endDateTime + "','" + alertValue + "','" + 1 + "','" + lat + "','"
					+ latDir + "','" + lon + "','" + longDir + "','" + address + "','" + gpsstatus + "','" + battStatus
					+ "','" + rawrssi + "','" + rssi + "','" + externalsensor + "','" + humidity + "','" + temperature
					+ "','" + light + "','" + motion + "','" + pressure + "','" + alerttypeId + "','" + gatewayId
					+ "','" + assetId + "','" + alertCfgId + "','" + companyId + "','" + timeZone + "','" 
					+ firstnotifiedtime + "','" + lastnotifiedtime + "','" +notifyCount + "',"+sms+","+mail+","
					+notify+","+minval+","+maxval+",'"+notificationtype+"','"+notificationtype+"','"+msgId+"');";
			
			updateCount = session.createSQLQuery(qry).executeUpdate();
			
			boolean stat = updateAlertMsg(startDateTime, alertCfgId,alertMsg);
			
			return updateCount > 0;
		} catch (Exception e) {
			log.error("InsertAlert : " + e.getLocalizedMessage());
		}

		return false;
	}

	@Override
	public long getAlertId(String startTime, long alertCfgId) {

		log.info("Entered into AlertNotifyHistoryDaoImplV4 :: getAlertId");
		long alertId = 0;
		Session session = sessionFactory.getCurrentSession();

		try {

			String qry = "select `id` from `alert` where `startdatetime`='" + startTime + "' and `alertcfg_id`="
					+ alertCfgId + " and `ack`=0;";
			
			List res = session.createSQLQuery(qry).list();
			if(res != null)
				alertId = ((BigInteger) res.get(0)).longValue();

			log.info("alertId : " + alertId + " : insertQry : " + qry);
			return alertId;
			
		} catch (Exception e) {
			log.error("getAlertId : " + e.getLocalizedMessage());
		}
		return 0;
	}
	
	public long getPLPBAlertId(String startTime, long alertCfgId) {

		log.info("Entered into AlertNotifyHistoryDaoImplV4 :: getAlertId");
		long alertId = 0;
		Session session = sessionFactory.getCurrentSession();

		try {

			String qry = "select `id` from `alert` where `startdatetime`='" + startTime + "' and `alertcfg_id`=" + alertCfgId + " order by id desc limit 1;";
			
			List res = session.createSQLQuery(qry).list();
			if(res != null)
				alertId = ((BigInteger) res.get(0)).longValue();

			log.info("alertId : " + alertId + " : insertQry : " + qry);
			return alertId;
			
		} catch (Exception e) {
			log.error("getAlertId :alertCfgId:"+alertCfgId+ ":" + e.getLocalizedMessage());
		}
		return 0;
	}
	
	public boolean updateAlertMsg(String startTime, long alertCfgId,String alertMsg ) {
		Session session = sessionFactory.getCurrentSession();
		try {
			long alertId = getPLPBAlertId(startTime, alertCfgId);
			
			Alert alert = null;
			Criteria criteria = session.createCriteria(Alert.class).add(Restrictions.eq("id", alertId));
			alert = (Alert) criteria.list().get(0);

			if(alert != null) {				
				alert.setAlert_msg(alertMsg);				
				session.merge(alert);
				return true;
			}
			return false;
			
		}catch (Exception e) {
			log.error("updateAlertMsg:alertCfgId : "+alertCfgId+" : " + e.getLocalizedMessage());
			return false;
			
		}
	}

	@Override
	public boolean updateplByAlertId(long alertId, String schedulerName) {
		log.info("Entered :: AlertNotifyHistoryDaoImplV4 :: updateplByAlertId::  ");

		try {
			Session ses = sessionFactory.getCurrentSession();

			String updateQry = "UPDATE `pl_alert_history` SET `alert_id`="+alertId+",`alert_sent`=1 WHERE scheduler_name='" + schedulerName + "'";

			log.info(" Update Query : " + updateQry);
			int updateCount = ses.createSQLQuery(updateQry).executeUpdate();

			if (updateCount == 0)
				return false;
			else
				return true;

		} catch (Exception e) {
			log.error("updateplByAlertId : " + e.getLocalizedMessage());

		}
		return false;
	}

	@Override
	public boolean UpdateAlertNotified(long alertId, String notifiedTime, String alertMsg,int sms,int mail,int notify,String msgId) {
		log.info("Entered :: AlertNotifyHistoryDaoImplV4 :: UpdateAlertNotified::  ");

		try {

			Session ses = sessionFactory.getCurrentSession();
			Alert alert = null;
			try {
				Session session = sessionFactory.getCurrentSession();
				Criteria criteria = session.createCriteria(Alert.class).add(Restrictions.eq("id", alertId));
				alert = (Alert) criteria.list().get(0);

				if(alert != null) {
					if(alert.getNotified_count()==0)
						alert.setFirstnotifiedtime(Timestamp.valueOf(notifiedTime));
					
					alert.setLastnotifiedtime(Timestamp.valueOf(notifiedTime));
					alert.setNotified_count(alert.getNotified_count()+1);
					
					alert.setSms_ncount(alert.getSms_ncount() + sms);
					alert.setMail_ncount(alert.getMail_ncount()+mail);
					alert.setNotify_ncount(alert.getNotify_ncount() + notify);
					
					alert.setAlert_msg(alertMsg);
					alert.setMsg_id(msgId);
					session.merge(alert);
				}
			} catch (IndexOutOfBoundsException e) {
				log.error("getAlertCfg : "+e.getLocalizedMessage());
			}
			
			
			return true;

		} catch (Exception e) {
			log.error("UpdateAlertNotified : " + e.getLocalizedMessage());

		}
		return false;
	}
	
	public boolean insertAlertStatus(String operation, long cmp_id, long gatewayId, long alerttype_id,
			boolean alert_status) {
		try {
			
			String qry = "select id from `alert_status` where alerttype_id =" + alerttype_id +"  and `gateway_id`=" + gatewayId + ""
					+ " and cmp_id=" + cmp_id + "";
			List res = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			if(res != null || ! res.isEmpty()) {
				operation = "update";
			}
				
			if (operation.equalsIgnoreCase("insert")) {
				qry = "INSERT INTO alert_status (gateway_id,alerttype_id,alert_status,cmp_id) values(" + gatewayId
						+ "," + alerttype_id + "," + alert_status + "," + cmp_id + ")";
			} else {
				qry = "update `alert_status` set " + "`alert_status`=" + alert_status + "  where alerttype_id ="
						+ alerttype_id + "  and `gateway_id`='" + gatewayId + "' and cmp_id='" + cmp_id + "';";
			}
			
			int status = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

			log.info("insertAlertStatus : " + qry);
			return true;
		} catch (Exception e) {
			log.error("insertAlertStatus:exp: " + e);
			return false;
		}
	}

	@Override
	public boolean saveAlertRangeNotification(AlertRangeNotification arNotification) {
		try {
			this.sessionFactory.getCurrentSession().save(arNotification);
			return true;
		}catch (Exception e) {
			e.printStackTrace();
			log.error("saveAlertRangeNotification: "+e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public AlertRangeNotification getAlertRangeNotification(long user_id, long gateway_id, long alerttype_id) {
		log.info("Entered into getAlertRangeNotification ");
		try {

			String qry = " SELECT * FROM alert_range_notification WHERE gateway_id="+gateway_id+" AND user_id="+user_id+" AND alerttype_id="
					+alerttype_id+" order by id desc limit 1;";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(AlertRangeNotification.class);
			AlertRangeNotification arnObj =  (AlertRangeNotification)query.list().get(0); 
			return arnObj;
		}catch (Exception e) {
			log.error("Error in getAlertRangeNotification: " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public AlertCfgV4 getAlertMinMax(long alertCfgId) {
		log.info("Entered getAlertMinMax :  ");

		AlertCfgV4 alert = new AlertCfgV4();

		try {
			Session session = this.sessionFactory.getCurrentSession();

			String qry = "SELECT minval,maxval from alertcfg where id=" + alertCfgId;

			List<Object[]> list = session.createSQLQuery(qry).list();

			if (list != null && !list.isEmpty()) {
				Object[] obj = list.get(0);

				float min = Float.valueOf(obj[0].toString());
				float max = Float.valueOf(obj[1].toString());
								
				alert.setMinval(min);
				alert.setMaxval(max);
				
				return alert;
				
			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("Exception in getAlertMinMax : " + e.getLocalizedMessage());
			return null;
		}
	}

	
	@Override
	public ArrayList<JTempRangeReport> getTempRangeReport(String date) {
		log.info("Entered getTempRangeReport :  ");

		ArrayList<JTempRangeReport> alertList = new ArrayList<JTempRangeReport>();

		try {
			Session session = this.sessionFactory.getCurrentSession();

			String qry = "SELECT g.name,u.username,u.id AS userid, remarks, pn_content FROM alert_range_notification ar " + 
					"JOIN gateway g ON g.id=ar.gateway_id JOIN `user` u ON u.id=ar.user_id WHERE DATE(created_at) = DATE('"+date+"'); " ;

			List<Object[]> list = (List<Object[]>)session.createSQLQuery(qry).list();

			if (list != null && !list.isEmpty()) {
				for (Object[] obj : list) {
					String gname = obj[0].toString();
					String username = obj[1].toString();
					String userid = obj[2].toString();
					String remarks = obj[3].toString();
					String pn_content = "NA";
					if(obj[4]!= null)
						pn_content = obj[4].toString();
					
					JTempRangeReport trp = new JTempRangeReport(gname, username, userid, remarks, pn_content);
					alertList.add(trp);
				}				
			} 
			return alertList;
		} catch (Exception e) {
			log.error("Exception in getTempRangeReport : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public ArrayList<JAlertCountReport> getAlertCountReport(String date) {
		log.info("Entered getTempRangeReport :  ");

		ArrayList<JAlertCountReport> alertList = new ArrayList<JAlertCountReport>();

		try {
			Session session = this.sessionFactory.getCurrentSession();

			String qry = "SELECT r.* FROM (SELECT  g.name AS gatewayname,g.id AS gatewayid,u.username,u.id AS userid,"
					+ "SUM(temp_sms+pl_sms+pb_sms+hum_sms+geo_sms+bat_sms+dnr_sms) AS Total_sms ,"
					+ "SUM(temp_mail+pl_mail+pb_mail+hum_mail+geo_mail+bat_mail+dnr_mail) AS Total_mail  ," + 
					" SUM(temp_notify+pl_notify+pb_notify+hum_notify+geo_notify+bat_notify+dnr_notify) AS Total_notification " + 
					" FROM `alertcount_daily` ad JOIN usergateway ug ON ug.gatewayid = ad.gateway_id" + 
					" JOIN `user` u ON ug.userid=u.id JOIN gateway g ON g.id = ug.gatewayid  WHERE ad.alert_date=DATE('"+date+"') "
					+ " GROUP BY g.id) AS r WHERE (r.Total_mail>5) OR (r.Total_sms>5) OR (Total_notification>5)" ;
			List<Object[]> list = (List<Object[]>)session.createSQLQuery(qry).list();

			if (list != null && !list.isEmpty()) {
				for (Object[] obj : list) {
					String gname = obj[0].toString();
					long gid = ((BigInteger) obj[1]).longValue();
					String username = obj[2].toString();
					long userid = ((BigInteger) obj[3]).longValue();
					int smscount = ((BigDecimal)obj[4]).intValue();
					int mailcount = ((BigDecimal)obj[5]).intValue();
					int notifycount = ((BigDecimal)obj[6]).intValue();
					
					JAlertCountReport trp = new JAlertCountReport(gname, gid, username, userid, smscount, mailcount, notifycount);
					alertList.add(trp);
				}				
			} 
			return alertList;
		} catch (Exception e) {
			log.error("Exception in getAlertCountReport : " + e.getLocalizedMessage());
			return null;
		}
	}
}
