package com.nimble.wagglealertservice.dao.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.nimble.wagglealertservice.dto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import com.nimble.wagglealertservice.Util.RemainderservicesUtil;
import com.nimble.wagglealertservice.dao.IDeviceStatusDao;
import com.nimble.wagglealertservice.dto.AlertCfgV4;
import com.nimble.wagglealertservice.dto.JAlert;
import com.nimble.wagglealertservice.dto.JDailyAlertTypeCount;
import com.nimble.wagglealertservice.dto.JGateway;
import com.nimble.wagglealertservice.dto.JLastGatewayRpt;

@Repository
public class DeviceStatusDaoImpl implements IDeviceStatusDao {

	@Autowired
	@Lazy
	private SessionFactory sessionFactory;

    @Value("${alertThresholdLimit1}")
    private int alertThresholdLimit1;

    @Value("${alertThresholdLimit2}")
    private int alertThresholdLimit2;

	private static final Logger log = LogManager.getLogger(DeviceStatusDaoImpl.class);

	@Override
	public ArrayList<JLastGatewayRpt> getLastGatewayReport(int count) {
		ArrayList<JLastGatewayRpt> gatewayList = new ArrayList<JLastGatewayRpt>();
		try {
			Session session = sessionFactory.openSession();
			String qry = "SELECT LGR.`datetime`,LGR.`gateway_id`,LGR.`timezone`,CC.`notreportinginterval`,CC.`gatewayname`,  G.`name`, G.`cmp_id`,G.`isenable`,C.name as cmpName FROM `lastgatewayreport` AS LGR "
					+ "JOIN `gateway` AS G ON LGR.`gateway_id` = G.`id` JOIN `companyconfig` AS CC ON G.`cmp_id` = CC.`cmp_id` JOIN `company` AS C ON G.`cmp_id` = C.`id` WHERE G.`onoffstatus` = 1 and C.`cmptype_id` = 3"
					+ " LIMIT " + count + ",1000;";

			List<Object[]> objList = (List<Object[]>) session.createSQLQuery(qry).list();

			for (Object[] obj : objList) {

				JLastGatewayRpt lgr = new JLastGatewayRpt();

				Timestamp dateTime = (Timestamp) obj[0];
				BigInteger gateway_id = (BigInteger) obj[1];
				String timeZone = (String) obj[2];
				BigInteger notReporting = (BigInteger) obj[3];
				String gatewayName = (String) obj[4];
				String actualGatewayName = (String) obj[5];
				BigInteger cmp_id = (BigInteger) obj[6];
				boolean isEnable = (boolean) obj[7];
				String cmpName =  (String) obj[8];
				lgr.setDateTime(dateTime);
				lgr.setGatewayId(gateway_id.longValue());
				lgr.setTimeZone(timeZone);
				lgr.setNotReporting(notReporting.longValue());
				lgr.setGatewayName(gatewayName);
				lgr.setActualGatewayName(actualGatewayName);
				lgr.setCompanyId(cmp_id.longValue());
				lgr.setEnable(isEnable);
				lgr.setCmpName(cmpName);
				log.info("gateway_id : " + gateway_id);
				gatewayList.add(lgr);
			}

		} catch (Exception e) {
			log.error("Get lastgatewayreport list : ", e.getLocalizedMessage());
			return null;
		}
		return gatewayList;
	}

	@Override
	public boolean isNRAlert(long companyId, String currentDateTime, long gatewayId) {
		String reqQry = null;
		boolean retValue = false;

		try {

			String qry1 = "SELECT a.`id` ,`alertstarttime`,`alertendtime` FROM `alertcfg` AS a "
					+ " JOIN alertcfg_to_asset AS ata ON a.id = ata.alertcfg_id WHERE a.`alerttype_id`=11  "
					+ "AND `enable`=1 AND ata.`asset_id`='" + gatewayId + "' AND a.`cmp_id`='" + companyId + "';";

			Session session = sessionFactory.getCurrentSession();

			List<Object[]> res = session.createSQLQuery(qry1).list();

			if (res != null && !res.isEmpty()) {
				Object[] obj = res.get(0);
				String alertStartTime = (Time) obj[1] + "";
				String alertStopTime = (Time) obj[2] + "";

				String qry = "SELECT `id` FROM"
						+ " `alertcfg` AS a JOIN alertcfg_to_asset AS ata ON a.id = ata.alertcfg_id "
						+ " WHERE a.`alerttype_id`= 11 AND a.`enable`=1 " + " AND a.`cmp_id`='" + companyId + "'";
				if (alertStartTime != null && !alertStartTime.equalsIgnoreCase("null") && alertStopTime != null
						&& !alertStopTime.equalsIgnoreCase("null")) {
					reqQry = qry + " AND ((`alertstarttime` <= `alertendtime` AND  (TIME('" + currentDateTime
							+ "')>=`alertstarttime` AND  TIME('" + currentDateTime + "')<=`alertendtime`)) "
							+ "OR (`alertendtime` < `alertstarttime` AND (TIME('" + currentDateTime
							+ "') <=`alertendtime` OR TIME('" + currentDateTime + "')>=`alertstarttime`)));";

					List res2 = session.createSQLQuery(reqQry).list();
					if (res2 != null && !res2.isEmpty())
						retValue = true;
					else {
						log.info("isNRAlert:No  records retrieved from alertconfig for this query -  " + reqQry);
						retValue = false;
					}
				} else {
					retValue = true;
				}
			} else {
				retValue = false;
			}
		} catch (Exception e) {
			log.error("isNRAlert:" + e);
		}
		return retValue;
	}

	@Override
	public AlertCfgV4 getAlertCfg(long assetId, long companyId,long alerttype_id) {
		AlertCfgV4 alertCfg = new AlertCfgV4();
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry1 = "SELECT a.`id` ,a.`intermittentfreq`,a.`severity`,a.`notifyfreq`,"
					+ "a.`notificationtype`,a.`emailids`,a.`mobileNos`,a.`country` FROM `alertcfg` AS a "
					+ " JOIN alertcfg_to_asset AS ata ON a.id = ata.alertcfg_id "
					+ "WHERE a.`alerttype_id`="+alerttype_id+"  AND `enable`=1 AND ata.asset_id=" + assetId + " AND a.`cmp_id`='"
					+ companyId + "';";

			List<Object> list = (List<Object>) session.createSQLQuery(qry1).list();
			if (list != null && !list.isEmpty()) {
				Object[] obj = (Object[]) list.get(0);
				long id = ((BigInteger) obj[0]).longValue();
				int intermittentFreq = (Integer) obj[1];
				int severity = (Integer) obj[2];
				int notifyFreq = (Integer) obj[3];
				String notificationType = (String) obj[4];
				String emailId = (String) obj[5];
				String mobileNos = (String) obj[6];
				String country = (String) obj[7];

				alertCfg.setId(id);
				alertCfg.setIntermittentfreq(intermittentFreq);
				alertCfg.setSeverity(severity);
				alertCfg.setNotifyfreq(notifyFreq);
				alertCfg.setNotificationtype(notificationType);
				alertCfg.setEmailids(emailId);
				alertCfg.setMobilenos(mobileNos);
				alertCfg.setCountry(country);
			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("getAlertCfg : " + e.getLocalizedMessage());
			return null;
		}
		return alertCfg;
	}

	@Override
	public JAlert getAlert(long alertCfgId, long assetId) {
		JAlert alert = new JAlert();
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry1 = "SELECT `id`,`count`,`startdatetime` " + " FROM `alert` WHERE `alertcfg_id`=" + alertCfgId
					+ " and `asset_id`='" + assetId + "' and `ack`=0;";

			List<Object[]> objList = (List<Object[]>) session.createSQLQuery(qry1).list();

			if (objList != null && !objList.isEmpty()) {
				Object[] obj = objList.get(0);
				long id = ((BigInteger) obj[0]).longValue();
				int count = (Integer) obj[1];
				String startDateTime = ((Timestamp) obj[2]).toString();
				alert.setId(id);
				alert.setCount(count);
				alert.setStartdatetime(startDateTime);
			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("getAlertCfg : " + e.getLocalizedMessage());
			return null;
		}
		return alert;
	}

	@Override
	public boolean updateAlert(long alertCfgId, String endDateTime, int count, long assetId) {
		Session session = sessionFactory.getCurrentSession();
		try {
			String qry = "update `alert` set " + "`enddatetime`='" + endDateTime + "', `count`=" + (++count)
					+ " where `alertcfg_id`=" + alertCfgId + " and `enddatetime`<'" + endDateTime + "' and `asset_id`='"
					+ assetId + "' and `ack`=0;";
			int updateCount = session.createSQLQuery(qry).executeUpdate();
			if (updateCount == 0)
				return false;
			else
				return true;
		} catch (Exception e) {
			log.error("updateAlert : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean insertAlert(String startDateTime, String endDateTime, String timeZone, float alertValue, String unit,
			double lat, String latDir, double lon, String longDir, String address, String gpsstatus, float battStatus,
			int rawrssi, String rssi, float externalsensor, float humidity, float temperature, float light,
			String motion, float pressure, long alerttypeId, long assetId, long gatewayId, long nodeId, long alertCfgId,
			long companyId) {

		String qry = "";
		Session session = sessionFactory.getCurrentSession();
		int updateCount = 0;
		try {
			if (nodeId != -1) {
				qry = "insert into `alert`(`startdatetime`,`enddatetime`,"
						+ "`alertvalue`,`count`, `lat`,`latDir`,`lon`,`lonDir`,`address`,`gpsstatus`,`battery`,`rawrssi`,`rssi`,"
						+ "`externalsensor`,`humidity`,`temperature`,`light`,`motion`,`pressure`,"
						+ "`alerttype_id`,`node_id`,`gateway_id`,`asset_id`,`alertcfg_id`,"
						+ "`cmp_id`,`timeZone`) values('" + startDateTime + "','" + endDateTime + "','" + alertValue
						+ "','" + 1 + "','" + lat + "','" + latDir + "','" + lon + "','" + longDir + "','" + address
						+ "','" + gpsstatus + "','" + battStatus + "','" + rawrssi + "','" + rssi + "','"
						+ externalsensor + "','" + humidity + "','" + temperature + "','" + light + "','" + motion
						+ "','" + pressure + "','" + alerttypeId + "','" + nodeId + "','" + gatewayId + "','" + assetId
						+ "','" + alertCfgId + "','" + companyId + "','" + timeZone + "');";

				updateCount = session.createSQLQuery(qry).executeUpdate();
			} else {

				qry = "insert into `alert`(`startdatetime`,`enddatetime`,"
						+ "`alertvalue`,`count`, `lat`,`latDir`,`lon`,`lonDir`,`address`,`gpsstatus`,`battery`,`rawrssi`,`rssi`,"
						+ "`externalsensor`,`humidity`,`temperature`,`light`,`motion`,`pressure`,"
						+ "`alerttype_id`,`gateway_id`,`asset_id`,`alertcfg_id`," + "`cmp_id`,`timeZone`) values('"
						+ startDateTime + "','" + endDateTime + "','" + alertValue + "','" + 1 + "','" + lat + "','"
						+ latDir + "','" + lon + "','" + longDir + "','" + address + "','" + gpsstatus + "','"
						+ battStatus + "','" + rawrssi + "','" + rssi + "','" + externalsensor + "','" + humidity
						+ "','" + temperature + "','" + light + "','" + motion + "','" + pressure + "','" + alerttypeId
						+ "','" + gatewayId + "','" + assetId + "','" + alertCfgId + "','" + companyId + "','"
						+ timeZone + "');";
				updateCount = session.createSQLQuery(qry).executeUpdate();

			}

			if (updateCount == 0)
				return false;
			else
				return true;
		} catch (Exception e) {
			log.error("Error in insertAlert : " + e.getMessage());
		}
		return false;
	}

	@Override
	public long getAlertId(String startDateTime, long alertCfgId) {
		long alertNo = 0;
		try {
			String qry = "select `id` from `alert` where `startdatetime`='" + startDateTime + "' and `alertcfg_id`="
					+ alertCfgId + " and `ack`=0;";
			Session session = sessionFactory.getCurrentSession();
			BigInteger obj = (BigInteger) session.createSQLQuery(qry).list().get(0);

			alertNo = obj.longValue();
		} catch (Exception e) {
			log.error("getAlertId:" + e);
		}

		return alertNo;
	}

	@Override
	public long[] getCreditsInfoForGateway(int gatewayId, int companyId) {

		long[] creditInfo = new long[4];
		log.info("getCreditsInfoForGateway: GatewayID =  " + gatewayId + " cmp id= " + companyId);

		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT SUM(`creditsSpent`) AS `totCreditsSpent`,SUM(`creditsAssigned`) AS `totCreditsAssnd`,SUM(`extraCreditsSpent`)  AS `totExtraCreditSpent`,"
					+ "SUM(`extraCreditsAssigned`) AS `totExCreditsAssnd` FROM `gatewaycredits`" + "WHERE (`cmp_id` = '"
					+ companyId + "');";

			Object[] gatewayCredits = (Object[]) session.createSQLQuery(qry).list().get(0);
			if (gatewayCredits != null) {

				creditInfo[0] = ((BigDecimal) gatewayCredits[0]).longValue();// total credits spent by all gateways
				creditInfo[1] = ((BigDecimal) gatewayCredits[1]).longValue();// total credits assigned to all gateways
				creditInfo[2] = ((BigDecimal) gatewayCredits[2]).longValue();// total extra credits spent by all
																				// gateways
				creditInfo[3] = ((BigDecimal) gatewayCredits[3]).longValue();// total extra credits for this company

			} else {
				log.info("getCreditsInfoForGateway: No records found for given companyid-" + companyId
						+ " and gateway_id= " + gatewayId);
				creditInfo = null;
			}

		} catch (Exception le) {
			log.error("getCreditsInfoForGateway " + le);
		}

		return creditInfo;
	}

	@Override
	public boolean updateThrottlingCount(long gatewayId, String gatewayname, long companyId, JGateway asset) {

		Session session = sessionFactory.getCurrentSession();
		long id = 0;
		try {
			String currdate = RemainderservicesUtil.getCurrentDateTime("yyyy-MM-dd", "+05:30");
			String query = "select id from `throttlingmonitor` where `gateway_id`='" + gatewayId + "' and `cmp_id`='"
					+ companyId + "' and date = '" + currdate + "';";

			List res = (List) session.createSQLQuery(query).list();

			log.info("Qry : " + query);
			if (!res.isEmpty()) {
				id = ((BigInteger) res.get(0)).longValue();
				log.info("Updating throttlingmonitor id " + id);

				String qry = "update `throttlingmonitor` set `noofreports`= `noofreports`+'" + asset.getRptsCnt() + "',"
						+ "`noofnormalreports`=`noofnormalreports`+'" + asset.getNrmlrptscnt()
						+ "',`noofsmsalerts` = `noofsmsalerts` + '" + asset.getSmsCnt() + "',`noofvoicealerts` = "
						+ "`noofvoicealerts` + '" + asset.getVoiceCnt() + "',`nooflocates` = `nooflocates` + " + "'"
						+ asset.getLocatesCnt() + "',`creditspent`=`creditspent`+'" + asset.getTotalCreditSpent()
						+ "',`noofemailalerts`=`noofemailalerts`+'" + asset.getEmailcnt()
						+ "',`noofpushnotifcns`=`noofpushnotifcns`+'" + asset.getPushnotificationcnt()
						+ "',`extracreditspent`=`extracreditspent`+'" + asset.getTotalExtraCreditsSpent()
						+ "' where id = '" + id + "';";

				int updateCount = session.createSQLQuery(qry).executeUpdate();
				if (updateCount == 0)
					return false;
				else
					return true;

			} else {
				log.info("Inserting throttlingmonitor id " + id);

				String qry = "insert into `throttlingmonitor`(`date`,`noofreports`,`noofsmsalerts`,`noofvoicealerts`,`nooflocates`,`cmp_id`,`gateway_id`,`noofnormalreports`,`noofemailalerts`,`noofpushnotifcns`,`creditspent`,`extracreditspent`) "
						+ "values('" + currdate + "','" + asset.getRptsCnt() + "','" + asset.getSmsCnt() + "','"
						+ asset.getVoiceCnt() + "','" + asset.getLocatesCnt() + "'," + "'" + companyId + "','"
						+ gatewayId + "','" + asset.getNrmlrptscnt() + "','" + asset.getEmailcnt() + "','"
						+ asset.getPushnotificationcnt() + "','" + asset.getCreditSpentperDay() + "','"
						+ asset.getExCreditSpentPerDay() + "');";

				int updateCount = session.createSQLQuery(qry).executeUpdate();
				if (updateCount == 0)
					return false;
				else
					return true;

			}

		} catch (Exception e) {
			log.error("updateGatewayStatus : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updatealertnotifyhistory(String endDateTime, String timeZone, long alertId, int notifyFreq) {

		String query = "update `alertnotifyhistory` set `endDateTime`='" + endDateTime + "', `timeZone`='" + timeZone
				+ "', notifyFreq = '" + notifyFreq + "' where `alertId`=" + alertId + " and `acknowledge`=0;";
		try {
			Session session = sessionFactory.getCurrentSession();

			int updateCount = session.createSQLQuery(query).executeUpdate();
			if (updateCount == 0)
				return false;
			else
				return true;
		} catch (Exception e) {
			log.error("updateGatewayStatus : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateGatewayStatus1(long gatewayid, int notReporting) {
		String query = "update `gateway` set `isalive`=" + notReporting + " where `id` IN(" + gatewayid + ");";
		try {
			Session session = sessionFactory.getCurrentSession();

			int updateCount = session.createSQLQuery(query).executeUpdate();
			if (updateCount == 1)
				return true;
			else
				return false;

		} catch (Exception e) {
			log.error("updateGatewayStatus : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public long getUserGateway(long gatewayId) {
		long userId = 0;
		log.info("getUserGateway : GatewayId = " + gatewayId);

		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT * FROM usergateway ug where ug.gatewayId=" + gatewayId;

			Object[] gatewayCredits = (Object[]) session.createSQLQuery(qry).list().get(0);
			if (gatewayCredits != null) {
				userId = ((BigInteger) gatewayCredits[0]).longValue();
			}

		} catch (Exception le) {
			log.error("getUserGateway " + le);
		}
		return userId;
	}

	@Override
	public boolean updateDailyAlertCount(JDailyAlertTypeCount jdatCount) {

		Session session = this.sessionFactory.getCurrentSession();
		long id = 0;
		try {

			String insertSubQry = "";
			String updateSubQry = "";
			int alertTypeId = (int) jdatCount.getAlerttype_id();
			int smsCount = jdatCount.getSms_count();
			int mailCount = jdatCount.getMail_count();
			int notifyCount = jdatCount.getNotify_count();

			switch (alertTypeId) {
			case 1:
				insertSubQry = "temp_mail,temp_sms,temp_notify";
				updateSubQry = "temp_mail=temp_mail+" + mailCount + ",  temp_sms=temp_sms+" + smsCount + ",  temp_notify=temp_notify+" + notifyCount;
				break;
			case 2:
				insertSubQry = "bat_mail,bat_sms,bat_notify";
				updateSubQry = "bat_mail=bat_mail+" + mailCount + ",  bat_sms=bat_sms+" + smsCount + ",  bat_notify=bat_notify+" + notifyCount;
				break;
			case 3:
				insertSubQry = "pl_mail,pl_sms,pl_notify";
				updateSubQry = "pl_mail=pl_mail+" + mailCount + ",  pl_sms=pl_sms+" + smsCount + ",  pl_notify=pl_notify+" + notifyCount;
				break;
			case 4:
				insertSubQry = "geo_mail,geo_sms,geo_notify";
				updateSubQry = "geo_mail=geo_mail+" + mailCount + ",  geo_sms=geo_sms+" + smsCount + ",  geo_notify=geo_notify+" + notifyCount;
				break;
			case 11:
				insertSubQry = "dnr_mail,dnr_sms,dnr_notify";
				updateSubQry = "dnr_mail=dnr_mail+" + mailCount + ",  dnr_sms=dnr_sms+" + smsCount + ",  dnr_notify=dnr_notify+" + notifyCount;
				break;
			case 14:
				insertSubQry = "hum_mail,hum_sms,hum_notify";
				updateSubQry = "hum_mail=hum_mail+" + mailCount + ",  hum_sms=hum_sms+" + smsCount + ",  hum_notify=hum_notify+" + notifyCount;
				break;
			case 17:
				insertSubQry = "pb_mail,pb_sms,pb_notify";
				updateSubQry = "pb_mail=pb_mail+" + mailCount + ",  pb_sms=pb_sms+" + smsCount + ",  pb_notify=pb_notify+" + notifyCount;
				break;

			}
			String currdate = RemainderservicesUtil.getCurrentDateTime("yyyy-MM-dd", "+05:30");
			String query = "select id from `alertcount_daily` where `gateway_id`='" + jdatCount.getGateway_id()
					+ "' and `cmp_id`='" + jdatCount.getCmp_id() + "' and alert_date = '" + currdate + "';";

			List res = (List) session.createSQLQuery(query).list();

			if (!res.isEmpty()) {
				id = ((BigInteger) res.get(0)).longValue();
				log.info("Updating alertcount_daily id: " + id);

				String qry = "update `alertcount_daily` set " + updateSubQry + " where id = " + id;
				int updateCount = session.createSQLQuery(qry).executeUpdate();
				if (updateCount == 0)
					return false;
				else
					return true;

			} else {
				String qry = "insert into alertcount_daily(alert_date,cmp_id,gateway_id,"+insertSubQry+") "
						+ "values('" + currdate + "'," + jdatCount.getCmp_id() + "," + jdatCount.getGateway_id() + ","
						+ mailCount + "," + smsCount + "," + notifyCount + ");";
				int updateCount = session.createSQLQuery(qry).executeUpdate();
				
				if (updateCount == 0)
					return false;
				else
					return true;
			}

		} catch (Exception e) {
			log.error("updateDailyAlertCount : " + e.getLocalizedMessage());
		}
		return false;
	}
	
	@Override
	public boolean updateGatewayShowVideo(long gatewayid, boolean isShow) {
		String query = "update gateway set show_temp_video=" + isShow + " where `id` =" + gatewayid + ";";
		try {
			Session session = sessionFactory.getCurrentSession();

			int updateCount = session.createSQLQuery(query).executeUpdate();
			if (updateCount == 1)
				return true;
			else
				return false;

		} catch (Exception e) {
			log.error("updateGatewayShowVideo : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public long getMonitortype( long gatewayId) {
		long monitor_type_id = 0;
		try {
			String qry = "SELECT `monitor_type_id` FROM `gateway` G JOIN assetmodel A ON G.model_id= A.id WHERE G.id="+gatewayId+";";
			Session session = sessionFactory.getCurrentSession();
			BigInteger obj = (BigInteger) session.createSQLQuery(qry).list().get(0);

			monitor_type_id = obj.longValue();
		} catch (Exception e) {
			log.error("getMonitortype:" + e.getLocalizedMessage());
		}

		return monitor_type_id;
	}


    @Override
    public boolean checkLastCreditNotification(long gatewayId,String featurecodeSms ,JUser juser) {

        log.info("checkLastCreditNotification: gatewayId: {}", gatewayId);
        try {
            String qry = getQry(gatewayId, juser, featurecodeSms);

            Session session = sessionFactory.getCurrentSession();
            Integer remainingLimit = (Integer) session.createSQLQuery(qry).list().get(0);

            if(remainingLimit<=alertThresholdLimit1 ) {
                return true;
            }
        }
        catch (Exception e) {
            log.error("checkLastCreditNotification: {}", e.getLocalizedMessage());
            return false;
        }
        return false;
    }

    private static String getQry(long gatewayId, JUser juser, String featurecodeSms) {
        String tableName = "gateway_feature";
        String whereKey= "gateway_id";
        long whereValue = gatewayId;
        if(juser.getPlan_ver().equalsIgnoreCase("V2")) {
            tableName = "user_feature";
            whereKey = "user_id";
            whereValue = juser.getUser_id();
        }

        String qry = "SELECT r.remaining_limit FROM " + tableName + " r "+
                "WHERE r.feature_code='"+featurecodeSms+"' AND r." + whereKey + " = " + whereValue +
                " AND r.enable=1 ORDER BY r.id DESC LIMIT 1;";
        return qry;
    }

    @Override
    public long     getAlertPushNotificationId(String featureCode) {

        log.info("getAlertPushNotificationId: featureCode: {}", featureCode);

        try {
            String qry = "SELECT id FROM pushnotifications WHERE feature_code ='"+featureCode+"'";
            BigInteger pushNotificationId = (BigInteger) this.sessionFactory
                    .getCurrentSession()
                    .createSQLQuery(qry)
                    .uniqueResult();

            return pushNotificationId.longValue();
        }
        catch (Exception e) {
            log.error("getAlertPushNotificationId: {}", e.getLocalizedMessage());
        }
        return 0;
    }

	@Override
	public boolean checkIsThreadSensor(long gatewayId) {

		try {

			String qry = "SELECT is_thread FROM gateway WHERE id =:gatewayId";
			Session session = sessionFactory.getCurrentSession();
			Query query = session.createSQLQuery(qry);
			query.setParameter("gatewayId", gatewayId);

			return (boolean) query.uniqueResult();
		}
		catch (Exception e) {
			log.error("checkIsThreadSensor: {}", e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public long getHubIdForSensor(long nodeId) {
		try {
			String qry = "SELECT hub_id FROM gateway WHERE id =:nodeId";
			Session session = sessionFactory.getCurrentSession();
			Query query = session.createSQLQuery(qry);
			query.setParameter("nodeId", nodeId);

			return ((BigInteger)(query.uniqueResult())).longValue();
		}
		catch (Exception e) {
			log.error("getHubIdForSensor: {}", e.getLocalizedMessage());
		}
		return 0;
	}
}
