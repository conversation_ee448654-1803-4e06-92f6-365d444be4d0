package com.nimble.wagglealertservice.dao.impl;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.wagglealertservice.Util.RemainderservicesUtil;
import com.nimble.wagglealertservice.dao.IScheduleAlertDao;
import com.nimble.wagglealertservice.entity.JScheduleAlert;

@Repository
public class ScheduleAlertDaoImpl implements IScheduleAlertDao {

	@Autowired
	private SessionFactory sessionFactory;

	private static final Logger log = LogManager.getLogger(ScheduleAlertDaoImpl.class);

	@Override
	public boolean createOrUpdateAlert(JScheduleAlert alert) {
		log.info("ScheduleAlertDaoImpl :: createOrUpdateAlert..");
		boolean isSuccess = false;
		try {
			sessionFactory.getCurrentSession().merge(alert);
			isSuccess = true;
			log.info("create or update : " + isSuccess);
		} catch (Exception e) {
			log.error("createOrUpdateAlert : "+alert.getGateway_id()+": " + e.getLocalizedMessage());
		}

		return isSuccess;
	}

	@Override
	public JScheduleAlert getScheduleAlertByName(String name) {
		log.info("ScheduleAlertDaoImpl :: getScheduleAlertByName..");
		JScheduleAlert jAlert = new JScheduleAlert();
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(JScheduleAlert.class)
					.add(Restrictions.eq("scheduler_name", name));
			List<JScheduleAlert> scheduleList = (List<JScheduleAlert>) criteria.list();

			jAlert = scheduleList.get(0);
			return jAlert;
		} catch (Exception e) {
			log.error("getScheduleAlertByName : " + e.getLocalizedMessage());
		}
		log.info("schedulername doesn't exist : list empty");
		return null;
	}

	@Override
	public boolean updateAlertByName(String name, int status) {
		log.info("ScheduleAlertDaoImpl :: updateAlertByName..");
		boolean isSuccess = false;
		try {
			JScheduleAlert jAlert = getScheduleAlertByName(name);

			if (jAlert != null) {
				if (status == -1) {
					jAlert.setEnable(-1);
				} else {
					jAlert.setEnable(0);
				}
				jAlert.setUpdated_at(RemainderservicesUtil.getCurrentTimeUTC());

				sessionFactory.getCurrentSession().merge(jAlert);
				isSuccess = true;
				log.info("updated status : " + isSuccess);
				return isSuccess;
			}
		} catch (Exception e) {
			log.error("updateAlertByName : " + e.getLocalizedMessage());
		}

		return isSuccess;
	}

	@Override
	public boolean updateScheduleAlert(long alertId, String schedulerName) {
		log.info("Entered :: ScheduleAlertDaoImpl :: updateScheduleAlert::  ");

		try {
			Session ses = sessionFactory.getCurrentSession();

			String updateQry = "UPDATE schedule_alert SET alert_id=" + alertId + " WHERE scheduler_name='"
					+ schedulerName + "'";

			log.info(" Update Query : " + updateQry);
			int updateCount = ses.createSQLQuery(updateQry).executeUpdate();

			if (updateCount == 0)
				return false;
			else
				return true;

		} catch (Exception e) {
			log.error("updateScheduleAlert : " + e.getLocalizedMessage());
		}
		return false;
	}

}
