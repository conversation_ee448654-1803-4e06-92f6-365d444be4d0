package com.nimble.wagglealertservice.dao;

import java.util.ArrayList;

import com.nimble.wagglealertservice.dto.AlertCfgV4;
import com.nimble.wagglealertservice.dto.JAlertNotifyHistory;
import com.nimble.wagglealertservice.entity.AlertRangeNotification;
import com.nimble.wagglealertservice.pojo.JAlertCountReport;
import com.nimble.wagglealertservice.pojo.JTempRangeReport;

public interface IAlertDaoV4 {

	public boolean ackExpiredAlerts(long alertid);

	public ArrayList<JAlertNotifyHistory> getAlertnotifyhistorylist();

	public JAlertNotifyHistory getAlertnotifyhistory(long alertId);

	public boolean UpdateAlertnotifyHistoryNotify(int count, String currentTime, long id, long alertId, String smsMsg,
			String timeZone);

	public boolean InsertAlertnotifyHistoryNotify(long alertId, long alertCfgId, String startDateTime,
			String endDateTime, int notifyFreq, String currentTime, String timeZone, int notifiedCount,
			String alertMsg);

	public boolean UpdateAlert(long gatewayId);

	public boolean DeleteplAlertHistoryStatus(long alertId);

	public boolean InsertAlert(String startDateTime, String endDateTime, String timeZone, float alertValue, String unit,
			double lat, String latDir, double lon, String longDir, String address, String gpsstatus, float battStatus,
			int rawrssi, String rssi, float externalsensor, float humidity, float temperature, float light,
			String motion, float pressure, long alerttypeId, long assetId, long gatewayId, long alertCfgId,
			long companyId, String firstnotifiedtime, String lastnotifiedtime, String alertMsg, int notifyCount,
			int sms,int mail,int notify,float minval,float maxval,String notificationtype,String msgId);

	public long getAlertId(String startTime, long alertCfgId);

	public boolean updateplByAlertId(long alertId, String schedulerName);

	public boolean UpdateAlertNotified(long alertId, String notifiedTime, String alertMsg,int sms,int mail,int notify,String msgId);
	
	public boolean insertAlertStatus(String operation, long cmp_id, long gatewayId, long alerttype_id,	boolean alert_status);

	public boolean saveAlertRangeNotification( AlertRangeNotification arNotification);
	
	public AlertRangeNotification getAlertRangeNotification( long user_id, long gatewayId, long alerttype_id);
	
	public AlertCfgV4 getAlertMinMax(long alertCfgId);
	
	public ArrayList<JTempRangeReport> getTempRangeReport(String date);

	public ArrayList<JAlertCountReport> getAlertCountReport(String date);
}
