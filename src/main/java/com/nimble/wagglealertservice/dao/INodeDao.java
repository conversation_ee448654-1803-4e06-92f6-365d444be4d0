package com.nimble.wagglealertservice.dao;

import java.util.ArrayList;

import com.nimble.wagglealertservice.dto.JLastNodeRpt;
import com.nimble.wagglealertservice.dto.JNode;

public interface INodeDao {

	public ArrayList<JLastNodeRpt> getLastNodeRpt();

	public boolean updateNodeStatus(long nodeId, int notReporting);

	public JNode getNode(long nodeId);

	public boolean isNodeEnabled(long nodeId);
}
