package com.nimble.wagglealertservice.dto;

import java.util.HashMap;
import java.util.Map;

public class JResponse {
	
	Map<String,Object> response;


	public JResponse() {
		super();
		response = new HashMap<String, Object>();
	}
	
	public Map<String, Object> getResponse() {
		return response;
	}

	public void setResponse(Map<String, Object> response) {
		this.response = response;
	}



	public void put(String string,  Object success) {

		response.put(string, success);
	}
}
