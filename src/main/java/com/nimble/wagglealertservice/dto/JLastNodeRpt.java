package com.nimble.wagglealertservice.dto;

import java.sql.Time;
import java.util.Date;

public class JLastNodeRpt {
	
	private Date date;
	private Time time;
	private long node_id;
	private String timezone;
	
	public JLastNodeRpt() {
		super();
	}

	public JLastNodeRpt(Date date, Time time, long node_id, String timezone) {
		super();
		this.date = date;
		this.time = time;
		this.node_id = node_id;
		this.timezone = timezone;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Time getTime() {
		return time;
	}

	public void setTime(Time time) {
		this.time = time;
	}

	public long getNode_id() {
		return node_id;
	}

	public void setNode_id(long node_id) {
		this.node_id = node_id;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}
	
	
}
