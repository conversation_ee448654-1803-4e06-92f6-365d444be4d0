package com.nimble.wagglealertservice.dto;

import java.sql.Timestamp;

public class JLastGatewayRpt {

	private Timestamp dateTime;
	private String timeZone;
	private long gatewayId = 0;
	private String gatewayName = "";
	private long notReporting;
	private long companyId;
	private String actualGatewayName = "";
	private boolean isEnable;
	private String cmpName="";
	public JLastGatewayRpt() {
		super();
	}

	public JLastGatewayRpt(Timestamp dateTime, String timeZone, long gatewayId, Timestamp lastDateTime,
			String gatewayName,String cmpName) {
		super();
		this.dateTime = dateTime;
		this.timeZone = timeZone;
		this.gatewayId = gatewayId;
		this.gatewayName = gatewayName;
		this.cmpName = cmpName;
	}
	public boolean isEnable() {
		return isEnable;
	}

	public void setEnable(boolean isEnable) {
		this.isEnable = isEnable;
	}

	public String getActualGatewayName() {
		return actualGatewayName;
	}

	public void setActualGatewayName(String actualGatewayName) {
		this.actualGatewayName = actualGatewayName;
	}

	public long getNotReporting() {
		return notReporting;
	}

	public void setNotReporting(long notReporting) {
		this.notReporting = notReporting;
	}

	public Timestamp getDateTime() {
		return dateTime;
	}


	public void setDateTime(Timestamp dateTime) {
		this.dateTime = dateTime;
	}


	public String getTimeZone() {
		return timeZone;
	}


	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}


	public long getGatewayId() {
		return gatewayId;
	}


	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}


	public String getGatewayName() {
		return gatewayName;
	}


	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(long companyId) {
		this.companyId = companyId;
	}

	public String getCmpName() {
		return cmpName;
	}

	public void setCmpName(String cmpName) {
		this.cmpName = cmpName;
	}
	
}
