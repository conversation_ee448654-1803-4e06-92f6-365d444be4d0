package com.nimble.wagglealertservice.dto;

public class JNode {
	
	private long id;
	private String name;
	private String macaddr;
	private boolean enable;
	private boolean alive;
	private String location;
	private String description;
	private String sensorEnable;
	private String macid = "NA";
    private long modelid;
    private String model;
    
    private long gatewayid;
	private String gatewayname;
	
    private long assetgroupid;
    private String assetgroupname;
    
    /*private long subgroupid;
    private String subgroupname;*/
    
    private long groupid;
    private String groupname;
    private String mdn;
    private String lastrptdatetime;
    private long cmp_id;
    
    
	public JNode() {
		super();
		// TODO Auto-generated constructor stub
	}
	public JNode(long id, String name, String macaddr, boolean isenable,
			boolean isalive, String location, String description,
		    String sensorEnable,long modelid, String model, long gatewayid,
			String gatewayname, long assetgroupid, String assetgroupname, 
			long groupid, String groupname,String mdn,String lastrptdatetime) {
		super();
		this.id = id;
		this.name = name;
		this.macaddr = macaddr;
		this.enable = isenable;
		this.alive = isalive;
		this.location = location;
		this.description = description;
		this.sensorEnable = sensorEnable;
		this.modelid = modelid;
		this.model = model;
		this.gatewayid = gatewayid;
		this.gatewayname = gatewayname;
		this.assetgroupid = assetgroupid;
		this.assetgroupname = assetgroupname;
/*		this.subgroupid = subgroupid;
		this.subgroupname = subgroupname;
*/		this.groupid = groupid;
		this.groupname = groupname;
		this.mdn = mdn;
		this.lastrptdatetime = lastrptdatetime;
	}
	
	public long getCmp_id() {
		return cmp_id;
	}
	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}
	public long getId() {
		return id;
	}
	public String getName() {
		return name;
	}
	public String getMacaddr() {
		return macaddr;
	}
	
	public String getLocation() {
		return location;
	}
	public String getDescription() {
		return description;
	}
	public String getSensorEnable() {
		return sensorEnable;
	}
	public long getModelid() {
		return modelid;
	}
	public String getModel() {
		return model;
	}
	public long getGatewayid() {
		return gatewayid;
	}
	public String getGatewayname() {
		return gatewayname;
	}
	public long getAssetgroupid() {
		return assetgroupid;
	}
	public String getAssetgroupname() {
		return assetgroupname;
	}
	/*public long getSubgroupid() {
		return subgroupid;
	}
	public String getSubgroupname() {
		return subgroupname;
	}*/
	public long getGroupid() {
		return groupid;
	}
	public String getGroupname() {
		return groupname;
	}
	public void setId(long id) {
		this.id = id;
	}
	public void setName(String name) {
		this.name = name;
	}
	public void setMacaddr(String macaddr) {
		this.macaddr = macaddr;
	}
	
	public void setLocation(String location) {
		this.location = location;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	

	public void setSensorEnable(String sensorEnable) {
		this.sensorEnable = sensorEnable;
	}
	public void setModelid(long modelid) {
		this.modelid = modelid;
	}
	public void setModel(String model) {
		this.model = model;
	}
	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}
	public void setGatewayname(String gatewayname) {
		this.gatewayname = gatewayname;
	}
	public void setAssetgroupid(long assetgroupid) {
		this.assetgroupid = assetgroupid;
	}
	public void setAssetgroupname(String assetgroupname) {
		this.assetgroupname = assetgroupname;
	}
	/*public void setSubgroupid(long subgroupid) {
		this.subgroupid = subgroupid;
	}
	public void setSubgroupname(String subgroupname) {
		this.subgroupname = subgroupname;
	}*/
	public void setGroupid(long groupid) {
		this.groupid = groupid;
	}
	public void setGroupname(String groupname) {
		this.groupname = groupname;
	}
	public boolean isEnable() {
		return enable;
	}
	public void setEnable(boolean enable) {
		this.enable = enable;
	}
	public boolean isAlive() {
		return alive;
	}
	public void setAlive(boolean alive) {
		this.alive = alive;
	}
	public String getMdn() {
		return mdn;
	}
	public void setMdn(String mdn) {
		this.mdn = mdn;
	}
	public String getLastrptdatetime() {
		return lastrptdatetime;
	}
	public void setLastrptdatetime(String lastrptdatetime) {
		this.lastrptdatetime = lastrptdatetime;
	}
	public String getMacid() {
		return macid;
	}
	public void setMacid(String macid) {
		this.macid = macid;
	}	
	
}
