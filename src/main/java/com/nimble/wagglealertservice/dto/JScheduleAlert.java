package com.nimble.wagglealertservice.dto;

public class JScheduleAlert {

	private long id;

	private long user_id;
	
	private long alert_id;

	private long gateway_id;

	private long cmp_id;

	private int enable;

	private String mobilenos = "NA";

	private String email = "NA";

	private String created_at;

	private String updated_at;

	private String scheduler_name = "NA";

	private String scheduler_time;

	private String cmp_name;
	
	private String  gateway_name;

	private String sms_msg;

	private String email_msg;
	
	private int notify_freq = 0;
	
	private long alertcfg_id;
	
	private String timezone;
	
	private int delaytime;

	private long creditspent = 0;

	private long extracreditspent = 0;
	
	private long noofsmsalerts = 0;
	
	private long noofemailalerts = 0;

	public JScheduleAlert() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public long getAlert_id() {
		return alert_id;
	}

	public void setAlert_id(long alert_id) {
		this.alert_id = alert_id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}

	public int getEnable() {
		return enable;
	}

	public void setEnable(int enable) {
		this.enable = enable;
	}

	public String getMobilenos() {
		return mobilenos;
	}

	public void setMobilenos(String mobilenos) {
		this.mobilenos = mobilenos;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getCreated_at() {
		return created_at;
	}

	public void setCreated_at(String created_at) {
		this.created_at = created_at;
	}

	public String getUpdated_at() {
		return updated_at;
	}

	public void setUpdated_at(String updated_at) {
		this.updated_at = updated_at;
	}

	public String getScheduler_name() {
		return scheduler_name;
	}

	public void setScheduler_name(String scheduler_name) {
		this.scheduler_name = scheduler_name;
	}

	public String getScheduler_time() {
		return scheduler_time;
	}

	public void setScheduler_time(String scheduler_time) {
		this.scheduler_time = scheduler_time;
	}

	public String getCmp_name() {
		return cmp_name;
	}

	public void setCmp_name(String cmp_name) {
		this.cmp_name = cmp_name;
	}

	public String getSms_msg() {
		return sms_msg;
	}

	public void setSms_msg(String sms_msg) {
		this.sms_msg = sms_msg;
	}

	public String getEmail_msg() {
		return email_msg;
	}

	public void setEmail_msg(String email_msg) {
		this.email_msg = email_msg;
	}
	
	public String getGateway_name() {
		return gateway_name;
	}

	public void setGateway_name(String gateway_name) {
		this.gateway_name = gateway_name;
	}

	public int getNotify_freq() {
		return notify_freq;
	}

	public void setNotify_freq(int notify_freq) {
		this.notify_freq = notify_freq;
	}

	public long getAlertcfg_id() {
		return alertcfg_id;
	}

	public void setAlertcfg_id(long alertcfg_id) {
		this.alertcfg_id = alertcfg_id;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public int getDelaytime() {
		return delaytime;
	}

	public void setDelaytime(int delaytime) {
		this.delaytime = delaytime;
	}

	public long getCreditspent() {
		return creditspent;
	}

	public void setCreditspent(long creditspent) {
		this.creditspent = creditspent;
	}

	public long getExtracreditspent() {
		return extracreditspent;
	}

	public void setExtracreditspent(long extracreditspent) {
		this.extracreditspent = extracreditspent;
	}

	public long getNoofsmsalerts() {
		return noofsmsalerts;
	}

	public void setNoofsmsalerts(long noofsmsalerts) {
		this.noofsmsalerts = noofsmsalerts;
	}

	public long getNoofemailalerts() {
		return noofemailalerts;
	}

	public void setNoofemailalerts(long noofemailalerts) {
		this.noofemailalerts = noofemailalerts;
	}
	
}
