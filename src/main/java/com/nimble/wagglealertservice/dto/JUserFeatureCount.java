package com.nimble.wagglealertservice.dto;

public class JUserFeatureCount {

	private long userid;
	private String featurecode;
	private int cnt;
	private long gatewayid;

	public JUserFeatureCount() {
		super();
	}	
	
	public JUserFeatureCount(long gatewayid,long userid, String featurecode, int cnt) {
		super();
		this.userid = userid;
		this.featurecode = featurecode;
		this.cnt = cnt;
		this.gatewayid = gatewayid;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public String getFeaturecode() {
		return featurecode;
	}

	public void setFeaturecode(String featurecode) {
		this.featurecode = featurecode;
	}

	public int getCnt() {
		return cnt;
	}
	public void setCnt(int cnt) {
		this.cnt = cnt;
	}

	public long getGatewayid() {
		return gatewayid;
	}

	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}

}
