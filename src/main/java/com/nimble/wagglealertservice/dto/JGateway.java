package com.nimble.wagglealertservice.dto;

public class JGateway {

	private int rptscnt = 0;
	private int nrmlrptscnt = 0;
	private long smscnt = 0;
	private long voicecnt = 0;
	private int locatescnt = 0;
	private long creditSpentPerDay = 0;// Credit spent by this gateway on one day , to set in throttling monitor table
	private long exCreditSpentPerDay = 0; // Extra Credits spent by this gateway on one day

	private long totalCreditSpent = 0;// Credit spent by all gateways of a company

	private long totalExtraCreditsSpent = 0;// extra credits spent by all gateways of this company

	private long emailcnt = 0;
	private long pushnotificationcnt = 0;
	private long totAssignedCredits = 0;// Credits assigned to all Gateways of a company
	private long totAssignedExtraCredits = 0;// extra credits assigned to all gateways per company
	private long cmp_id = 0;
	private long subGroup_id = 0;
	private String gatewayName = "";

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public long getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}

	public long getSubGroup_id() {
		return subGroup_id;
	}

	public void setSubGroup_id(long subGroup_id) {
		this.subGroup_id = subGroup_id;
	}

	public void setRptsCnt(int rptscnt) {
		this.rptscnt = rptscnt;
	}

	public int getRptsCnt() {
		return rptscnt;
	}

	public void setSmscnt(long smscnt) {
		this.smscnt = smscnt;
	}

	public long getSmsCnt() {
		return smscnt;
	}

	public void setVoicecnt(long voicecnt) {

		this.voicecnt = voicecnt;
	}

	public long getVoiceCnt() {
		return voicecnt;
	}

	public void setLocatesCnt(int locatescnt) {

		this.locatescnt = locatescnt;
	}

	public int getLocatesCnt() {
		return locatescnt;
	}

	public int getNrmlrptscnt() {
		return nrmlrptscnt;
	}

	public void setNrmlrptscnt(int nrmlrptscnt) {
		this.nrmlrptscnt = nrmlrptscnt;
	}

	public long getCreditSpentperDay() {
		return creditSpentPerDay;
	}

	public void setCreditSpentValues(long creditspent) {
		// Set when credits are spent
		this.creditSpentPerDay += creditspent; // to set in throttling monitor table
		this.totalCreditSpent += creditspent;// to calculate the available cumulative credits of all Gateways

	}

	public void setExtraCreditSpentValues(long extraCreditsSpent) {
		// Set when extra credits are spent
		this.exCreditSpentPerDay += extraCreditsSpent; // current spent value before updating DB
		this.totalExtraCreditsSpent += extraCreditsSpent;
	}

	public long getEmailcnt() {
		return emailcnt;
	}

	public void setEmailcnt(long emailcnt) {
		this.emailcnt = emailcnt;
	}

	public long getPushnotificationcnt() {
		return pushnotificationcnt;
	}

	public void setPushnotificationcnt(long pushnotificationcnt) {
		this.pushnotificationcnt = pushnotificationcnt;
	}

	public long getTotalCreditSpent() {
		return totalCreditSpent;
	}

	public void setTotalCreditSpent(long creditspent) {
		this.totalCreditSpent = creditspent;
	}

	public long getTotalAssignedCredits() {
		return totAssignedCredits;
	}

	public void setTotalAssignedCredits(long assignedCredits) {
		// set from DB
		this.totAssignedCredits = assignedCredits;
	}

	public long getTotalExtraCreditsSpent() {
		return totalExtraCreditsSpent;
	}

	public void setTotalExtraCreditsSpent(long extraCreditsSpent) {
		// set from DB
		this.totalExtraCreditsSpent = extraCreditsSpent;// total extra credits spent by all gateways of this company
	}

	public long getTotAssigExCredits() {
		return totAssignedExtraCredits;
	}

	public void setTotAssigExCredits(long extraCredits) {
		// set from DB
		this.totAssignedExtraCredits = extraCredits;
	}

	public long getExCreditSpentPerDay() {
		return exCreditSpentPerDay;
	}

}
