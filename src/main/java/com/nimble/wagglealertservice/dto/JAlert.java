package com.nimble.wagglealertservice.dto;

import com.nimble.wagglealertservice.entity.AssetInformation;

public class JAlert {
	
	private long id;
	private String startdatetime;
	private String enddatetime;
	private String timezone;
	private float alertvalue;
	private boolean acknowledged;
	private int noccurance;
	
	private int severity;
	private long alertcfgid;
	private String alertcfg;
	
	private long alerttypeid;
	private String alerttype;
	private long nodeid;
	private String node;
	private long gatewayid;
	private String gateway;
	/*private long subgroupid;
	private String subgroup;
*/	private long groupid;
	private String group;
	private double lat;
	private String latDir;
	private double lon;
	private String lonDir;
	private String address;

	private String gpsstatus;
	private int battery;
	private int rawrssi;
	private String rssi;
	private String motion;
	private float extsensor;
	private float humidity;
	private float temperature;
	private float light;
	private float pressure;
	private String firstnotifiedtime;
	private String lastnotifiedtime;
	private double lat_d;
	private	double lon_d;
	private int count;
	
	private AssetInformation assetInfo;
	
	public JAlert() {
		
	}
	
	public JAlert(long id, String startdatetime, String enddatetime,
			String timezone, float alertvalue, boolean acknowledged,
			int noccurance, int severity, long alertcfgid, String alertcfg,
			long alerttypeid, String alerttype, long nodeid, String node,
			long gatewayid, String gateway,
			long groupid, String group, double lat, String latDir,double lon, 
			String lonDir, String gpsstatus,
			int battery, int rawrssi, String rssi, String motion,
			float extsensor, float humidity, float temperature, float light,
			float pressure,AssetInformation assetInfo,String firstnotifiedtime,
			String lastnotifiedtime,double lat_d,double lon_d, String address) {
		super();
		this.id = id;
		this.startdatetime = startdatetime;
		this.enddatetime = enddatetime;
		this.timezone = timezone;
		this.alertvalue = alertvalue;
		this.acknowledged = acknowledged;
		this.noccurance = noccurance;
		this.severity = severity;
		this.alertcfgid = alertcfgid;
		this.alertcfg = alertcfg;
		this.alerttypeid = alerttypeid;
		this.alerttype = alerttype;
		this.nodeid = nodeid;
		this.node = node;
		this.gatewayid = gatewayid;
		this.gateway = gateway;
		/*this.subgroupid = subgroupid;
		this.subgroup = subgroup;*/
		this.groupid = groupid;
		this.group = group;
		this.lat = lat;
		this.latDir = latDir;
		this.lon = lon;
		this.lonDir = lonDir;
		this.gpsstatus = gpsstatus;
		this.battery = battery;
		this.rawrssi = rawrssi;
		this.rssi = rssi;
		this.motion = motion;
		this.extsensor = extsensor;
		this.humidity = humidity;
		this.temperature = temperature;
		this.light = light;
		this.pressure = pressure;
		this.assetInfo = assetInfo;
		this.firstnotifiedtime = firstnotifiedtime;
		this.lastnotifiedtime = lastnotifiedtime;
		this.lat_d = lat_d;
		this.lon_d = lon_d;
		this.address = address;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public void setId(long id) {
		this.id = id;
	}

	public void setStartdatetime(String startdatetime) {
		this.startdatetime = startdatetime;
	}

	public AssetInformation getAssetInfo() {
		return assetInfo;
	}

	public long getId() {
		return id;
	}

	public String getStartdatetime() {
		return startdatetime;
	}

	public String getEnddatetime() {
		return enddatetime;
	}

	public String getTimezone() {
		return timezone;
	}

	public float getAlertvalue() {
		return alertvalue;
	}

	public boolean isAcknowledged() {
		return acknowledged;
	}

	public int getNoccurance() {
		return noccurance;
	}

	public long getAlerttypeid() {
		return alerttypeid;
	}

	public String getAlerttype() {
		return alerttype;
	}

	public long getGatewayid() {
		return gatewayid;
	}

	public String getGateway() {
		return gateway;
	}

/*	public long getSubgroupid() {
		return subgroupid;
	}

	public String getSubgroup() {
		return subgroup;
	}
*/
	public long getGroupid() {
		return groupid;
	}

	public String getGroup() {
		return group;
	}

	public int getSeverity() {
		return severity;
	}

	public long getAlertcfgid() {
		return alertcfgid;
	}

	public String getAlertcfg() {
		return alertcfg;
	}

	public long getNodeid() {
		return nodeid;
	}

	public String getNode() {
		return node;
	}

	public double getLat() {
		return lat;
	}

	public double getLon() {
		return lon;
	}

	public String getLatDir() {
		return latDir;
	}

	public void setLatDir(String latDir) {
		this.latDir = latDir;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getLonDir() {
		return lonDir;
	}

	public void setLonDir(String lonDir) {
		this.lonDir = lonDir;
	}

	public String getGpsstatus() {
		return gpsstatus;
	}

	public int getBattery() {
		return battery;
	}

	public int getRawrssi() {
		return rawrssi;
	}

	public String getRssi() {
		return rssi;
	}

	public String getMotion() {
		return motion;
	}

	public float getExtsensor() {
		return extsensor;
	}

	public float getHumidity() {
		return humidity;
	}

	public float getTemperature() {
		return temperature;
	}

	public float getLight() {
		return light;
	}

	public float getPressure() {
		return pressure;
	}

	public String getFirstnotifiedtime() {
		return firstnotifiedtime;
	}

	public void setFirstnotifiedtime(String firstnotifiedtime) {
		this.firstnotifiedtime = firstnotifiedtime;
	}

	public String getLastnotifiedtime() {
		return lastnotifiedtime;
	}

	public void setLastnotifiedtime(String lastnotifiedtime) {
		this.lastnotifiedtime = lastnotifiedtime;
	}

	public double getLat_d() {
		return lat_d;
	}

	public double getLon_d() {
		return lon_d;
	}
}
