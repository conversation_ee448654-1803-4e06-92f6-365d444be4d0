package com.nimble.wagglealertservice.dto;

public class JDailyAlertTypeCount {
	
	private long gateway_id;
	private long cmp_id;
	private long alerttype_id;
	private int mail_count;
	private int sms_count;
	private int notify_count;
	
	public JDailyAlertTypeCount(long gateway_id, long cmp_id, long alerttype_id, int mail_count, int sms_count,
			int notify_count) {
		super();
		this.gateway_id = gateway_id;
		this.cmp_id = cmp_id;
		this.alerttype_id = alerttype_id;
		this.mail_count = mail_count;
		this.sms_count = sms_count;
		this.notify_count = notify_count;
	}
	
	public long getGateway_id() {
		return gateway_id;
	}
	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}
	public long getCmp_id() {
		return cmp_id;
	}
	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}
	public long getAlerttype_id() {
		return alerttype_id;
	}
	public void setAlerttype_id(long alerttype_id) {
		this.alerttype_id = alerttype_id;
	}
	public int getMail_count() {
		return mail_count;
	}
	public void setMail_count(int mail_count) {
		this.mail_count = mail_count;
	}
	public int getSms_count() {
		return sms_count;
	}
	public void setSms_count(int sms_count) {
		this.sms_count = sms_count;
	}
	public int getNotify_count() {
		return notify_count;
	}
	public void setNotify_count(int notify_count) {
		this.notify_count = notify_count;
	}

	
}
