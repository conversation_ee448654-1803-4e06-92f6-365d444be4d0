package com.nimble.wagglealertservice.dto;

public class JAlertNotifyHistory {

	private long id;

	private long alertId;

	private long contactMgrId;

	private String startDateTime;

	private String endDateTime;

	private int notifyFreqInSec;

	private int notifiedCount;

	private boolean acknowledge;

	private String smsAlertMsg;

	private String lastNotifiedTime;

	private String timeZone;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getAlertId() {
		return alertId;
	}

	public void setAlertId(long alertId) {
		this.alertId = alertId;
	}

	public long getContactMgrId() {
		return contactMgrId;
	}

	public void setContactMgrId(long contactMgrId) {
		this.contactMgrId = contactMgrId;
	}

	public String getStartDateTime() {
		return startDateTime;
	}

	public void setStartDateTime(String startDateTime) {
		this.startDateTime = startDateTime;
	}

	public String getEndDateTime() {
		return endDateTime;
	}

	public void setEndDateTime(String endDateTime) {
		this.endDateTime = endDateTime;
	}

	public int getNotifyFreqInSec() {
		return notifyFreqInSec;
	}

	public void setNotifyFreqInSec(int notifyFreqInSec) {
		this.notifyFreqInSec = notifyFreqInSec;
	}

	public int getNotifiedCount() {
		return notifiedCount;
	}

	public void setNotifiedCount(int notifiedCount) {
		this.notifiedCount = notifiedCount;
	}

	public boolean getAcknowledge() {
		return acknowledge;
	}

	public void setAcknowledge(boolean acknowledge) {
		this.acknowledge = acknowledge;
	}

	public String getSmsAlertMsg() {
		return smsAlertMsg;
	}

	public void setSmsAlertMsg(String smsAlertMsg) {
		this.smsAlertMsg = smsAlertMsg;
	}

	public String getLastNotifiedTime() {
		return lastNotifiedTime;
	}

	public void setLastNotifiedTime(String lastNotifiedTime) {
		this.lastNotifiedTime = lastNotifiedTime;
	}

	public String getTimeZone() {
		return timeZone;
	}

	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}

}
