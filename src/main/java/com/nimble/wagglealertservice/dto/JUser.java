package com.nimble.wagglealertservice.dto;

public class JUser {

	private long user_id = 0;

	private String plan_ver = "";

	private String authkey = "";
	public JUser() {
		
	}
	
	public JUser(long user_id, String plan_ver) {
		super();
		this.user_id = user_id;
		this.plan_ver = plan_ver;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getPlan_ver() {
		return plan_ver;
	}

	public void setPlan_ver(String plan_ver) {
		this.plan_ver = plan_ver;
	}

	public String getAuthkey() {
		return authkey;
	}

	public void setAuthkey(String authkey) {
		this.authkey = authkey;
	}	

}
