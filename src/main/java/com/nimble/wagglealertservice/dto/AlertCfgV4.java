package com.nimble.wagglealertservice.dto;

public class AlertCfgV4 {

	private long id ;
	private String name;
	private float minval 	= -70.0f;
	private float maxval 	= 170.0f;
	private String rule;
	private float radius;
	private int severity 	= 1;
	private boolean enable 	= false;	
	private int notifyfreq 	= 86400;
	private String mobilenos = "";
	private String country	= "NA";
	private String emailids;
	private String notificationtype = "0000";
   // private AlertType alerttype;
   // private Set<Asset> assets = new HashSet<Asset>();
    private long assetids;
    private long alerttypeid;
    private String alerttypename;
    private int intermittentfreq;
    
	public int getIntermittentfreq() {
		return intermittentfreq;
	}

	public void setIntermittentfreq(int intermittentfreq) {
		this.intermittentfreq = intermittentfreq;
	}

	public AlertCfgV4() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public float getMinval() {
		return minval;
	}

	public void setMinval(float minval) {
		this.minval = minval;
	}

	public float getMaxval() {
		return maxval;
	}

	public void setMaxval(float maxval) {
		this.maxval = maxval;
	}

	public String getRule() {
		return rule;
	}

	public void setRule(String rule) {
		this.rule = rule;
	}

	public float getRadius() {
		return radius;
	}

	public void setRadius(float radius) {
		this.radius = radius;
	}

	public int getSeverity() {
		return severity;
	}

	public void setSeverity(int severity) {
		this.severity = severity;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public int getNotifyfreq() {
		return notifyfreq;
	}

	public void setNotifyfreq(int notifyfreq) {
		this.notifyfreq = notifyfreq;
	}

	public String getMobilenos() {
		return mobilenos;
	}

	public void setMobilenos(String mobilenos) {
		this.mobilenos = mobilenos;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getEmailids() {
		return emailids;
	}

	public void setEmailids(String emailids) {
		this.emailids = emailids;
	}

	public String getNotificationtype() {
		return notificationtype;
	}

	public void setNotificationtype(String notificationtype) {
		this.notificationtype = notificationtype;
	}

	public long getAssetids() {
		return assetids;
	}

	public void setAssetids(long assetids) {
		this.assetids = assetids;
	}

	public long getAlerttypeid() {
		return alerttypeid;
	}

	public void setAlerttypeid(long alerttypeid) {
		this.alerttypeid = alerttypeid;
	}

	public String getAlerttypename() {
		return alerttypename;
	}

	public void setAlerttypename(String alerttypename) {
		this.alerttypename = alerttypename;
	}	
	
}
