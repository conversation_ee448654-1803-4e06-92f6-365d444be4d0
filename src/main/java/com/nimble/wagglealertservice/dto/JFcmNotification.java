package com.nimble.wagglealertservice.dto;

import java.util.ArrayList;
import java.util.List;

public class JFcmNotification {

	private String title = "";
	private String body = "";
	private String redirectUrl = "NA";
	private String imageUrl = "NA";
	private List<String> userTokenList;
	private String source = "";
	private String shortDescription = "NA";
	private long gatewayId = 0;
	private long hubId = 0;

	public JFcmNotification() {
		super();
	}
	
	
	public JFcmNotification(String title, String body, String redirectUrl, String imageUrl,
			List<String> userTokenList, String source, String shortDescription) {
		super();
		this.title = title;
		this.body = body;
		this.redirectUrl = redirectUrl;
		this.imageUrl = imageUrl;
		this.userTokenList = userTokenList;
		this.source = source;
		this.shortDescription = shortDescription;
	}


	public JFcmNotification(String title, String body, String redirectUrl, String imageUrl,
			ArrayList<String> userTokenList, String source, String shortDescription) {
		super();
		this.title = title;
		this.body = body;
		this.redirectUrl = redirectUrl;
		this.imageUrl = imageUrl;
		this.userTokenList = userTokenList;
		this.source = source;
		this.shortDescription = shortDescription;
	}
	
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getBody() {
		return body;
	}
	public void setBody(String body) {
		this.body = body;
	}
	
	public List<String> getUserTokenList() {
		return userTokenList;
	}


	public void setUserTokenList(List<String> userTokenList) {
		this.userTokenList = userTokenList;
	}


	public String getRedirectUrl() {
		return redirectUrl;
	}
	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}
	public String getImageUrl() {
		return imageUrl;
	}
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getShortDescription() {
		return shortDescription;
	}
	public void setShortDescription(String shortDescription) {
		this.shortDescription = shortDescription;
	}

	public long getGatewayId() {
		return gatewayId;
	}
	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	public long getHubId() {
		return hubId;
	}
	public void setHubId(long hubId) {
		this.hubId = hubId;
	}
}
