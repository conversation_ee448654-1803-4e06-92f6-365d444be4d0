package com.nimble.wagglealertservice.dto;

public class FeatureCredit {
	
	private int limit;

	private String feature_code;
	
	private boolean unlimited_cr = false;


	public FeatureCredit(int limit, String feature_code,boolean unlimited_cr) {
		super();
		this.limit = limit;
		this.feature_code = feature_code;
	}

	public long getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getFeature_code() {
		return feature_code;
	}

	public void setFeature_code(String feature_code) {
		this.feature_code = feature_code;
	}

	public boolean isUnlimited_cr() {
		return unlimited_cr;
	}

	public void setUnlimited_cr(boolean unlimited_cr) {
		this.unlimited_cr = unlimited_cr;
	}

}
