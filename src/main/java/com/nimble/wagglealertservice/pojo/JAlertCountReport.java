package com.nimble.wagglealertservice.pojo;


public class JAlertCountReport {

	private String gatewayName;
	
	private long gatewayid;
	
	private String username;

	private long userid;

	private int total_sms;

	private int total_mail;
	
	private int total_notification;
	
	public JAlertCountReport(String gatewayName, long gatewayid, String username, long userid, int total_sms,
			int total_mail, int total_notification) {
		super();
		this.gatewayName = gatewayName;
		this.gatewayid = gatewayid;
		this.username = username;
		this.userid = userid;
		this.total_sms = total_sms;
		this.total_mail = total_mail;
		this.total_notification = total_notification;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public long getGatewayid() {
		return gatewayid;
	}

	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public int getTotal_sms() {
		return total_sms;
	}

	public void setTotal_sms(int total_sms) {
		this.total_sms = total_sms;
	}

	public int getTotal_mail() {
		return total_mail;
	}

	public void setTotal_mail(int total_mail) {
		this.total_mail = total_mail;
	}

	public int getTotal_notification() {
		return total_notification;
	}

	public void setTotal_notification(int total_notification) {
		this.total_notification = total_notification;
	}
	
}
