package com.nimble.wagglealertservice.pojo;


/**
 * <AUTHOR>
 *
 */
public class CloudNotifications {
	
	/*Notification Type will be as follows,
	
	Type 1 - Only Text Message
	
	It has only short description and message body. It does not contain the image url
	
	Type 2 :
	
	It has short description and message body and it has image url
	
	Type 3. It has only short description and bannerurl
	
	*/
	private String body;

	private String sound;

	private String title;

	public String getBody() {
		return body;
	}

	public void setBody(String body) {
		this.body = body;
	}

	public String getSound() {
		return sound;
	}

	public void setSound(String sound) {
		this.sound = sound;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	
}
