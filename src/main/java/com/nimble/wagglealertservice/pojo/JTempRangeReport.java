package com.nimble.wagglealertservice.pojo;


public class JTempRangeReport {

	private String gatewayName;

	private String username;

	private String userid;

	private String remarks;

	private String pn_content;

	public JTempRangeReport(String gatewayName, String username, String userid, String remarks, String pn_content) {
		super();
		this.gatewayName = gatewayName;
		this.username = username;
		this.userid = userid;
		this.remarks = remarks;
		this.pn_content = pn_content;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getUserid() {
		return userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getPn_content() {
		return pn_content;
	}

	public void setPn_content(String pn_content) {
		this.pn_content = pn_content;
	}

	
}
