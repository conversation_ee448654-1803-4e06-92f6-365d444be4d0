package com.nimble.wagglealertservice.pojo;

public class FCMMultiNotification {

	private String[] registration_ids;

	private String priority;

	private JSendNotifications data;

	private CloudNotifications notification;

	private boolean content_available = true;

	private boolean mutable_content = true;

	public FCMMultiNotification(String[] androidRegId, String priority, JSendNotifications data,
			boolean content_available, boolean mutable_content) {
		super();
		this.registration_ids = androidRegId;
		this.priority = priority;
		this.data = data;
		this.content_available = content_available;
		this.mutable_content = mutable_content;
	}

	public FCMMultiNotification(String[] iosRegId, String priority, JSendNotifications data,
			CloudNotifications notification, boolean content_available, boolean mutable_content) {
		super();
		this.registration_ids = iosRegId;
		this.priority = priority;
		this.data = data;
		this.notification = notification;
		this.content_available = content_available;
		this.mutable_content = mutable_content;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public JSendNotifications getData() {
		return data;
	}

	public void setData(JSendNotifications data) {
		this.data = data;
	}

	public CloudNotifications getNotification() {
		return notification;
	}

	public void setNotification(CloudNotifications notification) {
		this.notification = notification;
	}

	public boolean isContent_available() {
		return content_available;
	}

	public void setContent_available(boolean content_available) {
		this.content_available = content_available;
	}

	public boolean isMutable_content() {
		return mutable_content;
	}

	public void setMutable_content(boolean mutable_content) {
		this.mutable_content = mutable_content;
	}

	public String[] getRegistration_ids() {
		return registration_ids;
	}

	public void setRegistration_ids(String[] registration_ids) {
		this.registration_ids = registration_ids;
	}

}
