package com.nimble.wagglealertservice.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="groupsb", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class GroupsB implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="groupBname")
	private String groupBname;

	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	private Company company;

	@ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	@JoinColumn(name = "groupsc_id")
	private GroupsC groupsc_id;

	public GroupsB(String groupBname, Company company, GroupsC groupsc_id) {
		super();
		this.groupBname = groupBname;
		this.company = company;
		this.groupsc_id = groupsc_id;
	}
	
	public GroupsB() {
		super();
		// TODO Auto-generated constructor stub
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getGroupBname() {
		return groupBname;
	}

	public void setGroupBname(String groupBname) {
		this.groupBname = groupBname;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public GroupsC getGroupsc_id() {
		return groupsc_id;
	}

	public void setGroupsC_id(GroupsC groupsc_id) {
		this.groupsc_id = groupsc_id;
	}
}
