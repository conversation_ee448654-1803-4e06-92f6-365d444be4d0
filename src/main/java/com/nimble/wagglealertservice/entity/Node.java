package com.nimble.wagglealertservice.entity;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity 
@Table(name="node", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Node  implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="id")
	/*@GenericGenerator(name="gen",strategy="identity")
	 * We should not have auto generation because node creation is based on asset id .
	 * 
	 * Asset id and node id should be shame
	 * 
	@GeneratedValue(generator="gen")*/
	private long id;
	@Column(name="name")
	private String name;
	@Column(name="macaddr")
	private String macaddr;
	@Column(name="isenable")
	private boolean enable;
	@Column(name="isalive")
	private boolean alive;
	@Column(name="location")
	private String location;
	@Column(name="description")
	private String description;
	@Column(name="sensorenable")
	private String sensorenable;
	@Column(name="extsensortype")
	private String extsensortype;
	@Column(name = "lastrptdatetime")
	private Timestamp lastrptdatetime;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
	//@JsonBackReference
    private Gateway gateway;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="asset_id")
	//@JsonBackReference
    private Asset asset;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="assetgroup_id")
	//@JsonBackReference
    private AssetGroup assetgroup;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="model_id")
	//@JsonBackReference
    private AssetModel model;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;
	
	

	public Node() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Node(long id, String name, String macaddr, boolean isenable,
			String location, String description, String sensorenable, 
			Gateway gateway,Asset asset,AssetGroup assetgroup, 
			AssetModel model,Company company,Timestamp lastrptdatetime) {
		super();
		this.id = id;
		this.name = name;
		this.macaddr = macaddr;
		this.enable = isenable;
		this.location = location;
		this.description = description;
		this.sensorenable = sensorenable;
		this.gateway = gateway;
		this.asset = asset;
		this.assetgroup = assetgroup;
		this.model = model;
		this.company = company;
		this.lastrptdatetime = lastrptdatetime;
	}

	public long getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public String getMacaddr() {
		return macaddr;
	}
	
	public boolean isEnable() {
		return enable;
	}

	public boolean isAlive() {
		return alive;
	}

	public String getLocation() {
		return location;
	}

	public String getDescription() {
		return description;
	}

	public String getSensorenable() {
		return sensorenable;
	}

	public String getExtsensortype() {
		return extsensortype;
	}

	public Gateway getGateway() {
		return gateway;
	}

	public Asset giveAsset() {
		return asset;
	}

	public AssetGroup getAssetgroup() {
		return assetgroup;
	}

	public AssetModel getModel() {
		return model;
	}

	public Company giveCompany() {
		return company;
	}

	public Timestamp getLastrptdatetime() {
		return lastrptdatetime;
	}

	public void setLastrptdatetime(Timestamp lastrptdatetime) {
		this.lastrptdatetime = lastrptdatetime;
	}
}
