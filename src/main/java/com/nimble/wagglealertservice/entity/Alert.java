package com.nimble.wagglealertservice.entity;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="alert")
public class Alert {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="startdatetime")
	private Timestamp startDateTime;
	
	@Column(name="enddatetime")
	private Timestamp endDateTime;
	
	@Column(name="timezone")
	private String timezone;
	
	@Column(name="alertvalue")
	private float alertValue;
	
	@Column(name="enable")
	private boolean enable =true;
	
	@Column(name="count")
	private int count = 1;
	
	@Column(name="ack")
	private boolean ack = false;
	
	@Column(name="lat")
	private double lat;
	
	@Column(name="latDir")
	private String latdir;
	
	@Column(name="lon")
	private double lon;
	
	@Column(name="lonDir")
	private String londir;
		
	@Column(name="address")
	private String address;
	
	@Column(name="gpsstatus")
	private String gpsstatus;
	
	@Column(name="battery")
	private int battery;
	
	@Column(name="rawrssi")
	private int rawrssi;
	
	@Column(name="rssi")
	private String rssi;
	
	@Column(name="motion")
	private String motion;
	
	@Column(name="externalsensor")
	private float extsensor;
	
	@Column(name="humidity")
	private float humidity;
	
	@Column(name="temperature")
	private float temperature;
	
	@Column(name="light")
	private float light;
	
	@Column(name="pressure")
	private float pressure;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="alerttype_id")

	private AlertType alerttype;
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="alertcfg_id")
	
    private AlertCfg alertcfg;
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="asset_id")	
    private Asset asset;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="node_id")	
    private Node node;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
    private Gateway gateway;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
    private Company company;
	
	@Column(name="firstnotifiedtime")
	private Timestamp firstnotifiedtime;
	
	@Column(name="lastnotifiedtime")
	private Timestamp lastnotifiedtime;
	
	@Column(name="alert_msg")
	private String alert_msg;
	
	@Column(name="notified_count")
	private float notified_count;
	
	@Column(name="sms_ncount")
	private int sms_ncount;
	
	@Column(name="mail_ncount")
	private int mail_ncount;
	
	@Column(name="notify_ncount")
	private int notify_ncount;

	@Column(name="msg_id")
	private String msg_id;

	public Alert() {
		super();
	}
	
	public long getId() {
		return id;
	}
	public Timestamp getStartDateTime() {
		return startDateTime;
	}
	public Timestamp getEndDateTime() {
		return endDateTime;
	}
	public String getTimezone() {
		return timezone;
	}
	public float getAlertValue() {
		return alertValue;
	}
	public boolean isEnable() {
		return enable;
	}
	public int getCount() {
		return count;
	}
	public AlertType getAlerttype() {
		return alerttype;
	}
	public AlertCfg getAlertcfg() {
		return alertcfg;
	}
	public Asset getAsset() {
		return asset;
	}
	public Gateway getGateway() {
		return gateway;
	}
	public Company getCompany() {
		return company;
	}
	public boolean getAck() {
		return ack;
	}
	public Node getNode() {
		return node;
	}
	
	public double getLat() {
		return lat;
	}
	public String getLatdir() {
		return latdir;
	}
	public double getLon() {
		return lon;
	}
	public String getLondir() {
		return londir;
	}
	public String getGpsstatus() {
		return gpsstatus;
	}
	public String getAddress() {
		return address;
	}
	public int getBattery() {
		return battery;
	}
	public int getRawrssi() {
		return rawrssi;
	}
	public String getRssi() {
		return rssi;
	}
	public String getMotion() {
		return motion;
	}
	public float getExtsensor() {
		return extsensor;
	}
	public float getHumidity() {
		return humidity;
	}
	public float getTemperature() {
		return temperature;
	}
	public float getLight() {
		return light;
	}
	public float getPressure() {
		return pressure;
	}
	public Timestamp getFirstnotifiedtime() {
		return firstnotifiedtime;
	}
	public void setFirstnotifiedtime(Timestamp firstnotifiedtime) {
		this.firstnotifiedtime = firstnotifiedtime;
	}
	public Timestamp getLastnotifiedtime() {
		return lastnotifiedtime;
	}
	public void setLastnotifiedtime(Timestamp lastnotifiedtime) {
		this.lastnotifiedtime = lastnotifiedtime;
	}
	public String getAlert_msg() {
		return alert_msg;
	}
	public void setAlert_msg(String alert_msg) {
		this.alert_msg = alert_msg;
	}
	public float getNotified_count() {
		return notified_count;
	}
	public void setNotified_count(float notified_count) {
		this.notified_count = notified_count;
	}
	public void setId(long id) {
		this.id = id;
	}
	public void setStartDateTime(Timestamp startDateTime) {
		this.startDateTime = startDateTime;
	}
	public void setEndDateTime(Timestamp endDateTime) {
		this.endDateTime = endDateTime;
	}
	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}
	public void setAlertValue(float alertValue) {
		this.alertValue = alertValue;
	}
	public void setEnable(boolean enable) {
		this.enable = enable;
	}
	public void setCount(int count) {
		this.count = count;
	}
	public void setAck(boolean ack) {
		this.ack = ack;
	}
	public void setLat(double lat) {
		this.lat = lat;
	}
	public void setLatdir(String latdir) {
		this.latdir = latdir;
	}
	public void setLon(double lon) {
		this.lon = lon;
	}
	public void setLondir(String londir) {
		this.londir = londir;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public void setGpsstatus(String gpsstatus) {
		this.gpsstatus = gpsstatus;
	}
	public void setBattery(int battery) {
		this.battery = battery;
	}
	public void setRawrssi(int rawrssi) {
		this.rawrssi = rawrssi;
	}
	public void setRssi(String rssi) {
		this.rssi = rssi;
	}
	public void setMotion(String motion) {
		this.motion = motion;
	}
	public void setExtsensor(float extsensor) {
		this.extsensor = extsensor;
	}
	public void setHumidity(float humidity) {
		this.humidity = humidity;
	}
	public void setTemperature(float temperature) {
		this.temperature = temperature;
	}
	public void setLight(float light) {
		this.light = light;
	}
	public void setPressure(float pressure) {
		this.pressure = pressure;
	}
	public void setAlerttype(AlertType alerttype) {
		this.alerttype = alerttype;
	}
	public void setAlertcfg(AlertCfg alertcfg) {
		this.alertcfg = alertcfg;
	}
	public void setAsset(Asset asset) {
		this.asset = asset;
	}
	public void setNode(Node node) {
		this.node = node;
	}
	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}
	public void setCompany(Company company) {
		this.company = company;
	}

	public int getSms_ncount() {
		return sms_ncount;
	}

	public void setSms_ncount(int sms_ncount) {
		this.sms_ncount = sms_ncount;
	}

	public int getMail_ncount() {
		return mail_ncount;
	}

	public void setMail_ncount(int mail_ncount) {
		this.mail_ncount = mail_ncount;
	}

	public int getNotify_ncount() {
		return notify_ncount;
	}

	public void setNotify_ncount(int notify_ncount) {
		this.notify_ncount = notify_ncount;
	}

	public String getMsg_id() {
		return msg_id;
	}

	public void setMsg_id(String msg_id) {
		this.msg_id = msg_id;
	}
}
