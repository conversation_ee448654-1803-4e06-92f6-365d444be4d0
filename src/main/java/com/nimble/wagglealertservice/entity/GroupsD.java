package com.nimble.wagglealertservice.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="groupsd", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class GroupsD implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="groupDname")
	private String groupDname;

	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	private Company company;
	
	@ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	@JoinColumn(name = "groupse_id")
	private GroupsE groupse_id;

	public GroupsD(String groupDname, Company company,
			GroupsE groupse_id) {
		super();
		this.groupDname = groupDname;
		this.company = company;
		this.groupse_id = groupse_id;
	}

	
	public GroupsD() {
		super();
		// TODO Auto-generated constructor stub
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getGroupDname() {
		return groupDname;
	}

	public void setGroupDname(String groupDname) {
		this.groupDname = groupDname;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public GroupsE getGroupse_id() {
		return groupse_id;
	}

	public void setGroupse_id(GroupsE groupse_id) {
		this.groupse_id = groupse_id;
	}	 
}
