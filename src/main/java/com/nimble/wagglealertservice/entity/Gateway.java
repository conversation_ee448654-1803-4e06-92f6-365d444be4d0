package com.nimble.wagglealertservice.entity;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;



@Entity 
@Table(name="gateway", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Gateway implements Serializable{
	
	@Id
	@Column(name="id")
	/*@GenericGenerator(name="gen",strategy="identity")
	 * We should not have auto generation because node creation is based on asset id .
	 * 
	 * Asset id and node id should be shame
	 * 
	@GeneratedValue(generator="gen")*/
	private long id;
	@Column(name="name")
	private String name;
	@Column(name="meid")
	private String meid;
	@Column(name="mdn")
	private String mdn;
	@Column(name="carrier")
	private String carrier;
	@Column(name="isenable")
	private boolean enable;
	@Column(name="isalive")
	private boolean alive; 
	@Column(name="location")
	private String location;
	@Column(name="description")
	private String description;
	@Column(name="sensorenable")
	private String sensorenable;
	@Column(name="extsensortype")
	private String extsensortype;
	@Column(name="owner")
	private String owner;
	@Column(name="minval")
	private float minTemp;
	@Column(name="maxval")
	private float maxTemp;
	@Column(name = "lastrptdatetime")
	private Timestamp lastrptdatetime;
	@Column(name="stopreport")
	private boolean stopreport;
	@Column(name="starttime")
	private String starttime;
	@Column(name="stoptime")
	private String stoptime;

	@Column(name="installed_date")
	private Timestamp installed_date;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="asset_id")
	//@JsonBackReference
    private Asset asset;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="assetgroup_id")
	//@JsonBackReference
    private AssetGroup assetgroup;
	
	/*@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="subgroup_id")
	//@JsonBackReference
    private SubGroup subgroup;*/
	

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="model_id")
	//@JsonBackReference
    private AssetModel model;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="passwordtype")
	//@JsonBackReference
    private DataP passwordtype;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	//@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;
	
	@ManyToMany(mappedBy = "gateways",fetch = FetchType.EAGER)
	private Set<User> users = new HashSet<User>();
	
	@Column(name="default_goal")
	private int default_goal;
	
	@Column(name="calories_goal")
	private int calories_goal;
	
	@Column(name="macid")
	private String macid;
	
	/*
	@ManyToMany(mappedBy = "gateways")
	//@JsonIgnore
    private Set<User> users = new HashSet<User>();*/
	
	public Gateway() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="assetinfo_id")
	//@JsonBackReference
    private AssetInformation assetinformation;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name="groups_id")
	//@JsonBackReference
    private Groups groups;
	
	@Column(name="qrcode")
	private String qrcode;
	
	@Column(name="onoffstatus")
	private boolean onOffStatus;
	
	@Column(name="gatewayconfig")
	private String gatewayConfig;
	
	@Column(name="onsleeptime")
	private String onSleepTime;
	
	@Column(name="offsleeptime")
	private String offSleepTime;
	
	@Column(name="show_order_id")
	private boolean showOrderId = true;
	

	public Gateway(long id, String name, String meid, String mdn,
			String carrier, boolean isenable, boolean isalive, String location,
			String description,String sensorenable, String owner,Asset asset,
			AssetGroup assetgroup, Groups groups, AssetModel model,
			Company company,Timestamp lastrptdatetime,boolean stopreport,String starttime,
			String stoptime,String qrcode,String gatewayConfig,String onSleepTime,String offSleepTime,
			int default_goal, int calories_goal,String macid) {
		super();
		this.id = id;
		this.name = name;
		this.meid = meid;
		this.mdn = mdn;
		this.carrier = carrier;
		this.enable = isenable;
		this.alive = isalive;
		this.location = location;
		this.description = description;
		this.sensorenable = sensorenable;
		this.owner = owner;
		this.asset = asset;
		this.assetgroup = assetgroup;
		this.groups = groups;
		this.model = model;
		this.company = company;
		this.lastrptdatetime = lastrptdatetime;
		this.stopreport = stopreport;
		this.starttime = starttime;
		this.stoptime = stoptime;
		this.qrcode=qrcode;
		this.gatewayConfig=gatewayConfig;
		this.onSleepTime=onSleepTime;
		this.offSleepTime=offSleepTime;
		this.default_goal=default_goal;
		this.calories_goal = calories_goal;
		this.macid = macid;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public String getMeid() {
		return meid;
	}

	public String getMdn() {
		return mdn;
	}

	public String getCarrier() {
		return carrier;
	}

	public boolean isEnable() {
		return enable;
	}

	public boolean isAlive() {
		return alive;
	}

	public String getLocation() {
		return location;
	}

	public String getDescription() {
		return description;
	}

	
	public String getSensorenable() {
		return sensorenable;
	}

	public String getExtsensortype() {
		return extsensortype;
	}

	public Asset giveAsset() {
		return asset;
	}

	public AssetGroup getAssetgroup() {
		return assetgroup;
	}

	/*public SubGroup getSubgroup() {
		return subgroup;
	}*/

	public AssetModel getModel() {
		return model;
	}

	public Company giveCompany() {
		return company;
	}

	public String getOwner() {
		return owner;
	}
	
	public Set<User> getUsers() {
		return users;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public void setUsers(Set<User> users) {
		this.users = users;
	}
	
	public AssetInformation getAssetinformation() {
		return assetinformation;
	}
	
	public void setAssetinformation(AssetInformation assetinformation) {
		this.assetinformation = assetinformation;
	}

	/*	public void setSubgroup(SubGroup subgroup) {
		this.subgroup = subgroup;
	}
*/	
	public Groups getGroups() {
		return groups;
	}
	
	public float getMinTemp() {
		return minTemp;
	}

	public void setMinTemp(float minTemp) {
		this.minTemp = minTemp;
	}

	public float getMaxTemp() {
		return maxTemp;
	}

	public void setMaxTemp(float maxTemp) {
		this.maxTemp = maxTemp;
	}

	public Timestamp getLastrptdatetime() {
		return lastrptdatetime;
	}

	public void setLastrptdatetime(Timestamp lastrptdatetime) {
		this.lastrptdatetime = lastrptdatetime;
	}
	
	public DataP giveDatap() {
		return passwordtype;
	}

	public void saveDatap(DataP datap) {
		this.passwordtype = datap;
	}

	public void setAlive(boolean alive) {
		this.alive = alive;
	}

	public boolean isStopreport() {
		return stopreport;
	}

	public void setStopreport(boolean stopreport) {
		this.stopreport = stopreport;
	}

	public String getStarttime() {
		return starttime;
	}

	public void setStarttime(String starttime) {
		this.starttime = starttime;
	}

	public String getStoptime() {
		return stoptime;
	}

	public void setStoptime(String stoptime) {
		this.stoptime = stoptime;
	}

	public DataP getPasswordtype() {
		return passwordtype;
	}

	public void setPasswordtype(DataP passwordtype) {
		this.passwordtype = passwordtype;
	}

	public Timestamp getInstalled_date() {
		return installed_date;
	}

	public void setInstalled_date(Timestamp installed_date) {
		this.installed_date = installed_date;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}

	public boolean isOnOffStatus() {
		return onOffStatus;
	}

	public void setOnOffStatus(boolean onOffStatus) {
		this.onOffStatus = onOffStatus;
	}

	public String getGatewayConfig() {
		return gatewayConfig;
	}

	public void setGatewayConfig(String gatewayConfig) {
		this.gatewayConfig = gatewayConfig;
	}

	public String getOnSleepTime() {
		return onSleepTime;
	}

	public void setOnSleepTime(String onSleepTime) {
		this.onSleepTime = onSleepTime;
	}

	public String getOffSleepTime() {
		return offSleepTime;
	}

	public void setOffSleepTime(String offSleepTime) {
		this.offSleepTime = offSleepTime;
	}

	public int getDefault_goal() {
		return default_goal;
	}

	public void setDefault_goal(int default_goal) {
		this.default_goal = default_goal;
	}

	public int getCalories_goal() {
		return calories_goal;
	}

	public void setCalories_goal(int calories_goal) {
		this.calories_goal = calories_goal;
	}

	public String getMacid() {
		return macid;
	}

	public boolean isShowOrderId() {
		return showOrderId;
	}

	public void setShowOrderId(boolean showOrderId) {
		this.showOrderId = showOrderId;
	}

	public void setMacid(String macid) {
		this.macid = macid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}
		
}
