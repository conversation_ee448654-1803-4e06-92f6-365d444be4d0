package com.nimble.wagglealertservice.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "company", uniqueConstraints = @UniqueConstraint(columnNames = { "id", "name" }))
public class Company implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	@Column(name = "name")
	private String name;
	@Column(name = "supervisor")
	private String supervisor;
	@Column(name = "email")
	private String email;
	@Column(name = "phoneno")
	private String phoneno;
	@Column(name = "mobileno")
	private String mobileno;
	@Column(name = "address")
	private String address;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "cmptype_id")
//  @JsonBackReference
	private CompanyType companytype;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "throttsettings_id")
	private ThrottlingSettings throtsettings;

//	@ManyToOne(cascade = CascadeType.ALL)
//	@JoinColumn(name="plan_id")
//  private SubscriptionPlan planid;

	@Transient
	private String throtsettingsid = "1";

	@Transient
	private String cmptype_id = "1";

	/*
	 * @Transient private int userslist;
	 */

	public Company() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Company(String name, String supervisor, String email, String phoneNo, String mobileNo, String address,
			ThrottlingSettings throtsettings, CompanyType cmpType) {
		super();
		this.name = name;
		this.supervisor = supervisor;
		this.email = email;
		this.phoneno = phoneNo;
		this.mobileno = mobileNo;
		this.address = address;
		this.throtsettings = throtsettings;
		this.companytype = cmpType;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSupervisor() {
		return supervisor;
	}

	public void setSupervisor(String supervisor) {
		this.supervisor = supervisor;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhoneno() {
		return phoneno;
	}

	public void setPhoneno(String phoneno) {
		this.phoneno = phoneno;
	}

	public String getMobileno() {
		return mobileno;
	}

	public void setMobileno(String mobileno) {
		this.mobileno = mobileno;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public ThrottlingSettings getThrotsettings() {
		return throtsettings;
	}

	public void setThrotsettings(ThrottlingSettings throtsettings) {
		this.throtsettings = throtsettings;
	}

	public String getThrotsettingsid() {
		return throtsettingsid;
	}

	public void setThrotsettingsid(String throtsettingsid) {
		this.throtsettingsid = throtsettingsid;
	}

	public CompanyType getCompanytype() {
		return companytype;
	}

	public void setCompanytype(CompanyType companytype) {
		this.companytype = companytype;
	}

	public String getCmptype_id() {
		return cmptype_id;
	}

	public void setCmptype_id(String cmptype_id) {
		this.cmptype_id = cmptype_id;
	}

	/*
	 * public SubscriptionPlan getPlanid() { return planid; }
	 * 
	 * public void setPlanid(SubscriptionPlan planid) { this.planid = planid; }
	 * 
	 * public int getUserslist() { return userslist; }
	 * 
	 * public void setUserslist(int userslist) { this.userslist = userslist; }
	 */

}
