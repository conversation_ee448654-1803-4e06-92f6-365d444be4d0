package com.nimble.wagglealertservice.entity;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

@Entity
@Table(name = "userverification")
public class UserVerification {

	public static final String STATUS_PENDING = "PENDING";
	public static final String STATUS_VERIFIED = "VERIFIED";

	public UserVerification() {
		super();
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "token")
	private String token;

	@Column(name = "status")
	private String status;

	@Column(name = "expireddatetime")
	private String expiredDateTime;

	@Column(name = "issueddatetime")
	private String issuedDateTime;

	@Column(name = "confirmeddatetime")
	private String confirmedDateTime;

	@Column(name = "resentlinkcount")
	private int resentLinkCount;

	@JsonIgnore
	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "user_id")
	private User user;

	public UserVerification(String expireTime) {
//		this.token = UUID.randomUUID().toString();
//		String uuid = UUID.randomUUID().toString();
//		this.token = AES.encrypt(uuid.substring(0,10));
		this.issuedDateTime = getCurrentTimeinUTC();
		this.expiredDateTime = getPlusDays(issuedDateTime, expireTime);
		this.resentLinkCount = 1;
		this.status = STATUS_PENDING;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public String getCurrentTimeinUTC() {

		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		df.setTimeZone(TimeZone.getTimeZone("UTC"));
		return df.format(d);
	}

	public String getPlusDays(String dateTime, String addDays) {

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		Calendar cal = Calendar.getInstance();

		try {
			cal.setTime(this.formatDate(dateTime));
		} catch (ParseException e) {
			return getCurrentTimeinUTC();
		}

		cal.add(Calendar.HOUR, Integer.parseInt(addDays));

		return df.format(cal.getTime());
	}

	public Date formatDate(String date) throws ParseException {

		Date formatedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date);

		return formatedDate;
	}

	public String getExpiredDateTime() {
		return expiredDateTime;
	}

	public void setExpiredDateTime(String expiredDateTime) {
		this.expiredDateTime = expiredDateTime;
	}

	public String getIssuedDateTime() {
		return issuedDateTime;
	}

	public void setIssuedDateTime(String issuedDateTime) {
		this.issuedDateTime = issuedDateTime;
	}

	public String getConfirmedDateTime() {
		return confirmedDateTime;
	}

	public void setConfirmedDateTime(String confirmedDateTime) {
		this.confirmedDateTime = confirmedDateTime;
	}

	public int getResentLinkCount() {
		return resentLinkCount;
	}

	public void setResentLinkCount(int resentLinkCount) {
		this.resentLinkCount = resentLinkCount;
	}

}
