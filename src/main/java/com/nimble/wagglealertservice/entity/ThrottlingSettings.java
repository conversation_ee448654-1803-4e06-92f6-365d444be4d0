package com.nimble.wagglealertservice.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="throttlingsettings", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class ThrottlingSettings implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="name")
	private String name;

	@Column(name="logins")
	private long logins;

	@Column(name="rptsperday")
	private long rptsperday;

	@Column(name="smsalerts")
	private long smsalerts;

	@Column(name="voicealerts")
	private long voicealerts;

	@Column(name="fencealerts_c")
	private long fencealerts_c;

	@Column(name="api")
	private long api;

	@Column(name="webapp")
	private int webapp;
	
	@Column(name="mobileapp")
	private int mobileapp;
	
	@Column(name="price")
	private long price;
	
	@Column(name="credits")
	private long credits;
	
	@Column(name="extra_credits")
	private long extra_credits;
	
	@Column(name="mobileNos")
	private long mobileNos;
	
	@Column(name="emailIds")
	private long emailIds;
	
	@Column(name="isCustomized")
	private int isCustomized;

	public ThrottlingSettings() {
		super();
	}	
	
	public ThrottlingSettings(long id, String name, long logins,
			long rptsperday, long smsalerts, long voicealerts,
			long fencealerts_c, long api, int webapp, int mobileapp, long price, long credits,
			long extra_credits, long mobileNos, long emailIds, int isCustomized) {
		super();
		this.id = id;
		this.name = name;
		this.logins = logins;
		this.rptsperday = rptsperday;
		this.smsalerts = smsalerts;
		this.voicealerts = voicealerts;
		this.fencealerts_c = fencealerts_c;
		this.api = api;
		this.webapp = webapp;
		this.mobileapp = mobileapp;
		this.price = price;
		this.credits = credits;
		this.extra_credits = extra_credits;
		this.mobileNos = mobileNos;
		this.emailIds = emailIds;
		this.isCustomized = isCustomized;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public long getLogins() {
		return logins;
	}

	public void setLogins(long logins) {
		this.logins = logins;
	}

	public long getRptsperday() {
		return rptsperday;
	}

	public void setRptsperday(long rptsperday) {
		this.rptsperday = rptsperday;
	}

	public long getSmsalerts() {
		return smsalerts;
	}

	public void setSmsalerts(long smsalerts) {
		this.smsalerts = smsalerts;
	}

	public long getVoicealerts() {
		return voicealerts;
	}

	public void setVoicealerts(long voicealerts) {
		this.voicealerts = voicealerts;
	}

	public long getFencealerts_c() {
		return fencealerts_c;
	}

	public void setFencealerts_c(long fencealerts_c) {
		this.fencealerts_c = fencealerts_c;
	}

	public long getApi() {
		return api;
	}

	public void setApi(long api) {
		this.api = api;
	}

	public int getWebapp() {
		return webapp;
	}

	public void setWebapp(int webapp) {
		this.webapp = webapp;
	}

	public int getMobileapp() {
		return mobileapp;
	}

	public void setMobileapp(int mobileapp) {
		this.mobileapp = mobileapp;
	}

	public long getPrice() {
		return price;
	}

	public void setPrice(long price) {
		this.price = price;
	}	
	
	public long getCredits() {
		return credits;
	}

	public void setCredits(long credits) {
		this.credits = credits;
	}

	public long getExtra_credits() {
		return extra_credits;
	}

	public void setExtra_credits(long extra_credits) {
		this.extra_credits = extra_credits;
	}

	public long getMobileNos() {
		return mobileNos;
	}

	public void setMobileNos(long mobileNos) {
		this.mobileNos = mobileNos;
	}

	public long getEmailIds() {
		return emailIds;
	}

	public void setEmailIds(long emailIds) {
		this.emailIds = emailIds;
	}

	public int getIsCustomized() {
		return isCustomized;
	}

	public void setIsCustomized(int isCustomized) {
		this.isCustomized = isCustomized;
	}
}
