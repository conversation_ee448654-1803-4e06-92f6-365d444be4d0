package com.nimble.wagglealertservice.entity;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "pushnotifications", uniqueConstraints = @UniqueConstraint(columnNames = { "id" }))
public class PushNotifications implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name = "id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;

	@Column(name = "shortdescription")
	private String shortDescription;

	@Column(name = "title")
	private String title;

	@Column(name = "message")
	private String message;

	@Column(name = "bannerimageurl")
	private String bannerImageUrl;

	@Column(name = "imageUrl")
	private String imageUrl;

	@Column(name = "createdon")
	private String createdOn;

	@Column(name = "expiryon")
	private String expiryOn;
	
	@Column(name = "hyperlink")
	private String hyperLink;

	@JsonIgnore
	@ManyToMany(cascade = { CascadeType.ALL }, fetch = FetchType.LAZY)
	@JoinTable(name = "userpushnotifications", joinColumns = {
			@JoinColumn(name = "pushnotificatoinId") }, inverseJoinColumns = { @JoinColumn(name = "userId") })
	private Set<User> users = new HashSet<User>();

	/*
	 * @ManyToMany(mappedBy = "gateways") //@JsonIgnore private Set<User> users
	 * = new HashSet<User>();
	 */
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="notificationtypeid")
	//@JsonBackReference
    private NotificationType notificationtype;
	

	@Transient
    private String notificationTypeID;

	public PushNotifications() {
		super();
		// TODO Auto-generated constructor stub
	}

	public PushNotifications(String shortDescritpion, String title, String message, String bannerImageUrl,
			String imageUrl, String createdOn, String expiryOn,  NotificationType notificationtype,String hyperLink) {
		super();
		this.shortDescription = shortDescritpion;
		this.title = title;
		this.message = message;
		this.bannerImageUrl = bannerImageUrl;
		this.imageUrl = imageUrl;
		this.createdOn = createdOn;
		this.expiryOn = expiryOn;
		this.notificationtype = notificationtype;
		this.hyperLink=hyperLink;
	}

	public PushNotifications(String shortDescritpion, String title, String message, String bannerImageUrl,
			String imageUrl, String createdOn, String expiryOn) {
		super();
		this.shortDescription = shortDescritpion;
		this.title = title;
		this.message = message;
		this.bannerImageUrl = bannerImageUrl;
		this.imageUrl = imageUrl;
		this.createdOn = createdOn;
		this.expiryOn = expiryOn;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getShortDescription() {
		return shortDescription;
	}

	public void setShortDescription(String shortDescritpion) {
		this.shortDescription = shortDescritpion;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getBannerImageUrl() {
		return bannerImageUrl;
	}

	public void setBannerImageUrl(String bannerImageUrl) {
		this.bannerImageUrl = bannerImageUrl;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public String getCreatedOn() {
		return createdOn=createdOn == null ? null : createdOn.substring(0, 19);
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getExpiryOn() {
		return expiryOn= expiryOn == null ? null : expiryOn.substring(0, 19);
	}

	public void setExpiryOn(String expiryOn) {
		this.expiryOn = expiryOn;
	}

	public Set<User> getUsers() {
		return users;
	}

	public void setUsers(Set<User> users) {
		this.users = users;
	}

	public NotificationType getNotificationtype() {
		return notificationtype;
	}

	public void setNotificationtype(NotificationType notificationtype) {
		this.notificationtype = notificationtype;
	}



	public String getNotificationTypeID() {
		return notificationTypeID;
	}

	public void setNotificationTypeID(String notificationTypeID) {
		this.notificationTypeID = notificationTypeID;
	}

	public String getHyperLink() {
		return hyperLink;
	}

	public void setHyperLink(String hyperLink) {
		this.hyperLink = hyperLink;
	}



}
