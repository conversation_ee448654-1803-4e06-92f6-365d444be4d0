package com.nimble.wagglealertservice.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="groupsc", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class GroupsC implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="groupCname")
	private String groupCname;

	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	private Company company;
	
	@ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	@JoinColumn(name = "groupsd_id")
	private GroupsD groupsd_id;

	
	public GroupsC(String groupCname, Company company, GroupsD groupsd_id) {
		super();
		this.groupCname = groupCname;
		this.company = company;
		this.groupsd_id = groupsd_id;
	}

	public GroupsC() {
		super();
		// TODO Auto-generated constructor stub
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getGroupCname() {
		return groupCname;
	}

	public void setGroupCname(String groupCname) {
		this.groupCname = groupCname;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public GroupsD getGroupsd_id() {
		return groupsd_id;
	}

	public void setGroupsd_id(GroupsD groupsd_id) {
		this.groupsd_id = groupsd_id;
	}	
}

