package com.nimble.wagglealertservice.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="notificationtype")
public class NotificationType  implements Serializable{
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="name")
	private String name;
	public long getId() {
		return id;
	}
	public String getName() {
		return name;
	}
	
	
	
}
