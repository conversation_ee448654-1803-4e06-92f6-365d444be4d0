package com.nimble.wagglealertservice.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="groups", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Groups  implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Size(min=1, max=40)
	@Column(name="groupname")
	private String name;
	
	 @JsonIgnore
	 @ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	 @JoinColumn(name="cmp_id")
	 private Company company;
	 
	 @Column(name = "grouplevel")
	 private long grouplevelid;
	 
	 @Column(name = "topgroupid")
	 private long topgroupid;

	 @ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	 @JoinColumn(name = "groupsb_id")
	 private GroupsB groupsb_id;
	
	 public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public long getGrouplevelid() {
		return grouplevelid;
	}

	public void setGrouplevelid(long grouplevelid) {
		this.grouplevelid = grouplevelid;
	}

	public long getTopgroupid() {
		return topgroupid;
	}

	public void setTopgroupid(long topgroupid) {
		this.topgroupid = topgroupid;
	}
	
	public GroupsB getGroupsb_id() {
		return groupsb_id;
	}

	public void setGroupsb_id(GroupsB groupsb_id) {
		this.groupsb_id = groupsb_id;
	}	 
	 
}
