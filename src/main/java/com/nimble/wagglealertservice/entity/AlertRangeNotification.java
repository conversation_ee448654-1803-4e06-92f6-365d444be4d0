package com.nimble.wagglealertservice.entity; 

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "alert_range_notification")
public class AlertRangeNotification {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name = "user_id")
	private long user_id;

	@Column(name = "gateway_id")
	private long gateway_id;

	@Column(name = "cmp_id")
	private long cmp_id;
	
	@Column(name = "alerttype_id")
	private long alerttype_id;

	@Column(name = "created_at")
	private String created_at;

	@Column(name = "alert_value")
	private float alert_value = 0.0f;

	@Column(name = "mail_content")
	private String mail_content="NA";
	
	@Column(name = "pn_content")
	private String pn_content="NA";
	
	@Column(name = "pn_datetime")
	private String pn_datetime ="1753-01-01 00:00:00";
	
	@Column(name = "mail_datetime")
	private String mail_datetime = "1753-01-01 00:00:00";
	
	@Column(name = "remarks")
	private String remarks="NA";
	
	public AlertRangeNotification() {
		super();
	}

	public AlertRangeNotification(long user_id, long gateway_id, long cmp_id, String created_at,
			long alerttype_id,float alert_value) {
		super();
		this.user_id = user_id;
		this.gateway_id = gateway_id;
		this.cmp_id = cmp_id;
		this.created_at = created_at;
		this.alerttype_id = alerttype_id;
		this.alert_value = alert_value;
	}
		

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}

	public String getCreated_at() {
		return created_at;
	}

	public void setCreated_at(String created_at) {
		this.created_at = created_at;
	}
	
	public float getAlert_value() {
		return alert_value;
	}

	public void setAlert_value(float alert_value) {
		this.alert_value = alert_value;
	}

	public String getMail_content() {
		return mail_content;
	}

	public void setMail_content(String mail_content) {
		this.mail_content = mail_content;
	}

	public String getPn_content() {
		return pn_content;
	}

	public void setPn_content(String pn_content) {
		this.pn_content = pn_content;
	}

	public String getPn_datetime() {
		return pn_datetime;
	}

	public void setPn_datetime(String pn_datetime) {
		this.pn_datetime = pn_datetime;
	}

	public String getMail_datetime() {
		return mail_datetime;
	}

	public void setMail_datetime(String mail_datetime) {
		this.mail_datetime = mail_datetime;
	}
	
	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public long getAlerttype_id() {
		return alerttype_id;
	}

	public void setAlerttype_id(long alerttype_id) {
		this.alerttype_id = alerttype_id;
	}

}
