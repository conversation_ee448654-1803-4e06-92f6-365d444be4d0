package com.nimble.wagglealertservice.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="assetmodel", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class AssetModel  implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="model")
	private String model;
	
	@Column(name="assettype")
	private String assettype;
	@Column(name="sensoravailable")
	private String sensoravailable;
	@Column(name="extsensortype")
	private String extensortype;
	@Column(name="description")
	private String description;
	@Column(name="inventorymodelname")
	private String inventorymodelname;
	
	@Column(name="isgps")
	private String isgps;
	
	@Column(name="isadvmode")
	private boolean isadvmode;

	@Column(name="ispowermode")
	private boolean ispowermode;

	@Column(name="is_ble")
	private boolean is_ble;
	
	@Column(name="is_upgrade")
	private boolean is_upgrade;
	
	@Column(name="ishumidity")
	private boolean ishumidity;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="monitor_type_id")
	private MonitorType monitor_type;
	
	public long getId() {
		return id;
	}
	public String getModel() {
		return model;
	}
	public String getAssettype() {
		return assettype;
	}

	public String getSensoravailable() {
		return sensoravailable;
	}
	public String getExtensortype() {
		return extensortype;
	}
	public String getDescription() {
		return description;
	}
	public String getInventorymodelname() {
		return inventorymodelname;
	}
	public void setInventorymodelname(String inventorymodelname) {
		this.inventorymodelname = inventorymodelname;
	}
	public String getIsgps() {
		return isgps;
	}
	public void setIsgps(String isgps) {
		this.isgps = isgps;
	}
	public MonitorType getMonitor_type() {
		return monitor_type;
	}
	public void setMonitor_type(MonitorType monitor_type) {
		this.monitor_type = monitor_type;
	}
	public boolean isAdvmode() {
		return isadvmode;
	}
	public void setAdvmode(boolean isadvmode) {
		this.isadvmode = isadvmode;
	}
	public boolean isPowermode() {
		return ispowermode;
	}
	public void setPowermode(boolean ispowermode) {
		this.ispowermode = ispowermode;
	}
	public boolean isBle() {
		return is_ble;
	}
	public void setBle(boolean is_ble) {
		this.is_ble = is_ble;
	}
	public boolean isIs_upgrade() {
		return is_upgrade;
	}
	public void setIs_upgrade(boolean is_upgrade) {
		this.is_upgrade = is_upgrade;
	}
	public boolean isIshumidity() {
		return ishumidity;
	}
	public void setIshumidity(boolean ishumidity) {
		this.ishumidity = ishumidity;
	}	
	
}
