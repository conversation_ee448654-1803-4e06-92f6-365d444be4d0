package com.nimble.wagglealertservice.entity; 

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "schedule_alert")
public class JScheduleAlert {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "user_id")
	private long user_id;

	@Column(name = "alert_id")
	private long alert_id;

	@Column(name = "gateway_id")
	private long gateway_id;

	@Column(name = "cmp_id")
	private long cmp_id;

	@Column(name = "enable")
	private int enable;

	@Column(name = "mobilenos")
	private String mobilenos = "NA";

	@Column(name = "email")
	private String email = "NA";

	@Column(name = "created_at")
	private String created_at;

	@Column(name = "updated_at")
	private String updated_at;

	@Column(name = "scheduler_name")
	private String scheduler_name = "NA";

	@Column(name = "schedule_time")
	private String scheduler_time;

	@Column(name = "cmp_name")
	private String cmp_name;
	
	@Column(name = "gateway_name")
	private String  gateway_name;

	@Column(name = "sms_msg")
	private String sms_msg;

	@Column(name = "email_msg")
	private String email_msg;
	
	@Column(name = "app_msg")
	private String app_msg;
	
	@Column(name = "notify_freq")
	private int notify_freq = 0;
	
	@Column(name = "alertcfg_id")
	private long alertcfg_id;
	
	@Column(name = "timezone")
	private String timezone;
	
	@Column(name = "alerttype_id")
	private long alerttype_id = 0;
	
	@Column(name = "alert_enable")
	private boolean alert_enable;
	
	@Column(name = "alertvalue")
	private float alertvalue;
	
	@Column(name = "pkt_datetime")
	private String pkt_datetime;
	
	@Column(name = "featurecode_MAIL")
	private String featurecode_MAIL = "NA";
	
	@Column(name = "featurecode_SMS")
	private String featurecode_SMS = "NA";
	
	@Column(name = "featurecode_NOTIFY")
	private String featurecode_NOTIFY = "NA";
	
	@Column(name = "plan_ver")
	private String plan_ver = "V1";
	
	@Column(name = "featurecode_alert")
	private String featurecode_alert = "NA";
	
	@Column(name = "minval")
	private float minval=0;
	
	@Column(name = "maxval")
	private float maxval = 0;
	
	@Column(name="notificationtype")
	private String notificationtype="0000";
	

	public JScheduleAlert() {
		super();
	}

	
	public JScheduleAlert(long user_id, long alert_id, long gateway_id, long cmp_id, int enable,
			String mobilenos, String email, String created_at, String updated_at, String scheduler_name,
			String scheduler_time, String cmp_name, String gateway_name, String sms_msg, String email_msg,
			String app_msg, int notify_freq, long alertcfg_id, String timezone, long alerttype_id, boolean alert_enable,
			float alertvalue, String pkt_datetime, String featurecode_MAIL, String featurecode_SMS,
			String featurecode_NOTIFY, String plan_ver, String featurecode_alert,float minval,float maxval,String notificationtype) {
		super();
		this.user_id = user_id;
		this.alert_id = alert_id;
		this.gateway_id = gateway_id;
		this.cmp_id = cmp_id;
		this.enable = enable;
		this.mobilenos = mobilenos;
		this.email = email;
		this.created_at = created_at;
		this.updated_at = updated_at;
		this.scheduler_name = scheduler_name;
		this.scheduler_time = scheduler_time;
		this.cmp_name = cmp_name;
		this.gateway_name = gateway_name;
		this.sms_msg = sms_msg;
		this.email_msg = email_msg;
		this.app_msg = app_msg;
		this.notify_freq = notify_freq;
		this.alertcfg_id = alertcfg_id;
		this.timezone = timezone;
		this.alerttype_id = alerttype_id;
		this.alert_enable = alert_enable;
		this.alertvalue = alertvalue;
		this.pkt_datetime = pkt_datetime;
		this.featurecode_MAIL = featurecode_MAIL;
		this.featurecode_SMS = featurecode_SMS;
		this.featurecode_NOTIFY = featurecode_NOTIFY;
		this.plan_ver = plan_ver;
		this.featurecode_alert = featurecode_alert;
		this.minval = minval;
		this.maxval = maxval;
		this.notificationtype = notificationtype;

	}
	
	public String getNotificationtype() {
		return notificationtype;
	}


	public void setNotificationtype(String notificationtype) {
		this.notificationtype = notificationtype;
	}


	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public long getAlert_id() {
		return alert_id;
	}

	public void setAlert_id(long alert_id) {
		this.alert_id = alert_id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}

	public int getEnable() {
		return enable;
	}

	public void setEnable(int enable) {
		this.enable = enable;
	}

	public String getMobilenos() {
		return mobilenos;
	}

	public void setMobilenos(String mobilenos) {
		this.mobilenos = mobilenos;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getCreated_at() {
		return created_at;
	}

	public void setCreated_at(String created_at) {
		this.created_at = created_at;
	}

	public String getUpdated_at() {
		return updated_at;
	}

	public void setUpdated_at(String updated_at) {
		this.updated_at = updated_at;
	}

	public String getScheduler_name() {
		return scheduler_name;
	}

	public void setScheduler_name(String scheduler_name) {
		this.scheduler_name = scheduler_name;
	}

	public String getScheduler_time() {
		return scheduler_time;
	}

	public void setScheduler_time(String scheduler_time) {
		this.scheduler_time = scheduler_time;
	}

	public String getCmp_name() {
		return cmp_name;
	}

	public void setCmp_name(String cmp_name) {
		this.cmp_name = cmp_name;
	}

	public String getSms_msg() {
		return sms_msg;
	}

	public void setSms_msg(String sms_msg) {
		this.sms_msg = sms_msg;
	}

	public String getEmail_msg() {
		return email_msg;
	}

	public void setEmail_msg(String email_msg) {
		this.email_msg = email_msg;
	}
	
	public String getGateway_name() {
		return gateway_name;
	}

	public void setGateway_name(String gateway_name) {
		this.gateway_name = gateway_name;
	}

	public int getNotify_freq() {
		return notify_freq;
	}

	public void setNotify_freq(int notify_freq) {
		this.notify_freq = notify_freq;
	}

	public long getAlertcfg_id() {
		return alertcfg_id;
	}

	public void setAlertcfg_id(long alertcfg_id) {
		this.alertcfg_id = alertcfg_id;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public long getAlerttype_id() {
		return alerttype_id;
	}

	public void setAlerttype_id(long alerttype_id) {
		this.alerttype_id = alerttype_id;
	}

	public boolean isAlert_enable() {
		return alert_enable;
	}

	public void setAlert_enable(boolean alert_enable) {
		this.alert_enable = alert_enable;
	}

	public float getAlertvalue() {
		return alertvalue;
	}

	public void setAlertvalue(float alertvalue) {
		this.alertvalue = alertvalue;
	}

	public String getPkt_datetime() {
		return pkt_datetime;
	}

	public void setPkt_datetime(String pkt_datetime) {
		this.pkt_datetime = pkt_datetime;
	}

	public String getFeaturecode_MAIL() {
		return featurecode_MAIL;
	}

	public String getFeaturecode_SMS() {
		return featurecode_SMS;
	}

	public String getPlan_ver() {
		return plan_ver;
	}

	public void setFeaturecode_MAIL(String featurecode_MAIL) {
		this.featurecode_MAIL = featurecode_MAIL;
	}

	public void setFeaturecode_SMS(String featurecode_SMS) {
		this.featurecode_SMS = featurecode_SMS;
	}

	public void setPlan_ver(String plan_ver) {
		this.plan_ver = plan_ver;
	}

	public String getFeaturecode_alert() {
		return featurecode_alert;
	}

	public void setFeaturecode_alert(String featurecode_alert) {
		this.featurecode_alert = featurecode_alert;
	}

	public String getFeaturecode_NOTIFY() {
		return featurecode_NOTIFY;
	}

	public void setFeaturecode_NOTIFY(String featurecode_NOTIFY) {
		this.featurecode_NOTIFY = featurecode_NOTIFY;
	}

	public String getApp_msg() {
		return app_msg;
	}

	public void setApp_msg(String app_msg) {
		this.app_msg = app_msg;
	}
	
	public float getMinval() {
		return minval;
	}

	public void setMinval(float minval) {
		this.minval = minval;
	}

	public float getMaxval() {
		return maxval;
	}

	public void setMaxval(float maxval) {
		this.maxval = maxval;
	}
}
