package com.nimble.wagglealertservice.helper;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.sql.Time;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import com.nimble.wagglealertservice.pojo.JAlertCountReport;
import com.nimble.wagglealertservice.pojo.JTempRangeReport;
import com.opencsv.CSVWriter;

public class Helper {

	private static final Logger log = LogManager.getLogger(Helper.class);

	char hexDigit[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };

	public Date combineDateTime(Date date, Time time) {
		Calendar calendarA = Calendar.getInstance();
		calendarA.setTime(date);
		Calendar calendarB = Calendar.getInstance();
		calendarB.setTime(time);

		calendarA.set(Calendar.HOUR_OF_DAY, calendarB.get(Calendar.HOUR_OF_DAY));
		calendarA.set(Calendar.MINUTE, calendarB.get(Calendar.MINUTE));
		calendarA.set(Calendar.SECOND, calendarB.get(Calendar.SECOND));
		calendarA.set(Calendar.MILLISECOND, calendarB.get(Calendar.MILLISECOND));

		Date result = calendarA.getTime();
		return result;
	}

	public String getTimeZone(String timeZone) {

		String displayTimezone = "";
		if (timeZone != "") {
			displayTimezone = "," + timeZone;
		}
		return displayTimezone;
	}

	public String getNodeDetails(String nodeName) {
		String nodeDetails = "";
		if (null != nodeName)
			nodeDetails = "Node:  " + nodeName + ",\r\n";
		return nodeDetails;
	}

	public static String setDateAndTime(String time) {
		DateFormat inFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		DateFormat outFormat = new SimpleDateFormat("d MMM yyyy,hh:mm a");
		Date date = null;
		try {
			date = inFormat.parse(time);
			if (date != null)
				return outFormat.format(date);
		} catch (Exception e) {
			log.error("Error : " + e.getLocalizedMessage());
		}
		return time;
	}

	public static long getTimestampInMillis(Timestamp ts, String timeZone) {
		long timeMillis = 0;
		Date date;

		try {
			Calendar cal = Calendar.getInstance();
			cal.setTimeZone(TimeZone.getTimeZone(timeZone));

			// create a new Date object using the timezone of the specified city
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.s");
			sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
			date = sdf.parse(ts.toString());

			cal.setTime(date);
			timeMillis = cal.getTimeInMillis();
		} catch (ParseException e) {
			log.error("updateSampledReport: " + e);
		}

		return timeMillis;
	}

	public static String getCurrentDateTime(String timeZone) {
		Calendar curCal = getCurrentTimeCal("GMT" + timeZone);

		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		dateFormat.setTimeZone(TimeZone.getTimeZone("GMT" + timeZone));
		String dateStr = dateFormat.format(curCal.getTime());

		return dateStr;
	}

	public static Calendar getCurrentTimeCal(String timeZone) {
		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone(timeZone));
		currDateCal.setTime(new Date(System.currentTimeMillis()));

		return currDateCal;
	}

	public String getCurrentTimeinUTC() {

		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		df.setTimeZone(TimeZone.getTimeZone("UTC"));
		log.info(df.format(d));
		return df.format(d);
	}

//	public JSONObject getJSONObject(String reponse) {
//
//		log.info("Convert response string to JSON Object");
//
//		JSONObject jresponse = new JSONObject();
//
//		Gson gson = new Gson();
//		try {
//			JSONObject nresponse = new JSONObject(reponse);
//			jresponse = nresponse.getJSONObject("response");
//			return jresponse;
//		} catch (JSONException e) {
//			log.error("Error : Convert response string to JSON Object  : " + e.getLocalizedMessage());
//			return null;
//		}
//
//	}

	public static boolean callFCMAPI(String key, StringEntity params) {

		CloseableHttpClient httpClient = HttpClientBuilder.create().build();

		try {
			log.info("call  FCM");
			HttpPost request = new HttpPost("https://fcm.googleapis.com/fcm/send");

			String authKey = "key=" + key;
			request.addHeader("content-type", "application/json");
			request.addHeader("Authorization", authKey);
			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			log.info("FCM request: " + request + "FCM request param: " + params);
			log.info("FCM result: " + result.toString());

			return true;
		} catch (IOException e) {
			log.info("CheckLicence Exception:" + e);
			return false;
		} catch (Exception e) {
			log.info("CheckLicence Exception :" + e);
			return false;
		} finally {
			try {
				httpClient.close();
			} catch (Exception e) {
				log.info("CheckLicence Exception :" + e);
			}
		}

	}
	
	public static int callIrisFcm(String url, String json) {

		CloseableHttpClient httpClient = HttpClientBuilder.create().build();

		try {
			log.info("call iris FCM API");
			HttpPost request = new HttpPost(url);

			request.addHeader("content-type", "application/json");
			request.setEntity(new StringEntity(json));
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			JSONObject respone = new JSONObject(result.toString());
			if (respone.has("status") && respone.getInt("status") > 0) {
				log.info(respone.getString("Msg"));
				return respone.getInt("MsgCount");
			} else
				return 0;
		} catch (IOException e) {
			log.info("CheckLicence Exception:" + e);
			return 0;
		} catch (Exception e) {
			log.info("CheckLicence Exception :" + e);
			return 0;
		} finally {
			try {
				httpClient.close();
			} catch (Exception e) {
				log.info("CheckLicence Exception :" + e);
			}
		}

	}

	public boolean deleteFile(File file) {
		log.info("Entered into deleteFile");
		try {
			boolean deleteStatus = file.delete();
			log.info("Filename : " + file.getName() + ":: File deleted status : " + deleteStatus);
			return true;
		} catch (Exception e) {
			log.error(
					"Error in delete File :: FileName : " + file.getName() + " :: Error : " + e.getLocalizedMessage());
		}
		return false;
	}
	
	public boolean deleteMultipeFiles(List<File> files) {
		log.info("Entered into deleteMultipeFiles");
		try {

			for( File file : files ) {
				try {
					boolean deleteStatus = file.delete();
					log.info("Filename : "+ file.getName() +":: File deleted status : "+deleteStatus);
				} catch (Exception e) {
					log.error("Error in delete File :: FileName : "+ file.getName() +" :: Error : "+e.getLocalizedMessage());
				}
			}
			
			return true;
		} catch (Exception e) {
			log.error("Error in deleteMultipeFiles :: Error : "+e.getLocalizedMessage());
		}
		return false;
	}
	
	public File writeCSVDataLineByLine(List<JTempRangeReport> rpts, String rptDate) {
		log.info("Entered into writeCSVDataLineByLine");
		try {
			String fileName = "TempRangeReport_" + rptDate + ".csv";

			File file = new File(fileName);
			if (file.exists()) {
				file.delete();
				System.out.println("File already exists");
			}
			CSVWriter csvWriter = new CSVWriter(new FileWriter(file));
			String[] header = { "GatewayName","UserName","UserId","Remarks","Notification Content" };
			
			// Write headers
			csvWriter.writeNext(header);
			
			int length = rpts.size();
			for (int i = 0; i < length; i++) {
				try {
					 String gatewayName = rpts.get(i).getGatewayName();

					 String username = rpts.get(i).getUsername();

					 String userid= rpts.get(i).getUserid();

					 String remarks= rpts.get(i).getRemarks();

					 String pn_content= rpts.get(i).getPn_content();	
					 
					csvWriter.writeNext(new String[] {gatewayName,username,userid,remarks,pn_content});
				} catch (Exception e) {
					log.error("Error While Write Data Row by Row " + e.getMessage());
				}
			}
			csvWriter.close();
			return file;
		} catch (Exception e) {
			log.error("Error in writeCSVDataLineByLine :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}

	public File writeAlertCountCSVDataLineByLine(List<JAlertCountReport> rpts, String rptDate) {
		log.info("Entered into writeAlertCountCSVDataLineByLine");
		try {
			String fileName = "AlertCountReport_for" + rptDate + ".csv";

			File file = new File(fileName);
			if (file.exists()) {
				file.delete();
				log.info("File already exists."+fileName + " deleted.");
			}
			CSVWriter csvWriter = new CSVWriter(new FileWriter(file));
			String[] header = { "GatewayName","GatewayId","UserName","UserId","SMSCount","MailCount","NotificationCount" };
			
			// Write headers
			csvWriter.writeNext(header);
			
			for (JAlertCountReport rpt : rpts) {
				try {
					 String gatewayName = rpt.getGatewayName();
					 String gid = String.valueOf(rpt.getGatewayid());
					 String username = rpt.getUsername();

					 String  userid= String.valueOf(rpt.getUserid());

					 String smscount= String.valueOf(rpt.getTotal_sms());

					 String mailcount= String.valueOf(rpt.getTotal_mail());
					 
					 String notifycount = String.valueOf(rpt.getTotal_notification());
					 
					csvWriter.writeNext(new String[] {gatewayName,gid,username,userid,smscount,mailcount,notifycount});
				} catch (Exception e) {
					log.error("Error While Write alertcount Data Row by Row " + e.getMessage());
				}
			}
			csvWriter.close();
			return file;
		} catch (Exception e) {
			log.error("Error in writeAlertCountCSVDataLineByLine :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}

	public File convertMultipleFilesToZip(List<File> files, String fileName) {
		log.info("Entered into convertZip");
		try {
			String zipFileName = fileName.concat(".zip");
			
			File file = new File(zipFileName);
			if (file.exists()) {
				file.delete();
				log.info("File already exists."+fileName + " deleted.");
			}
			
			FileOutputStream fos = new FileOutputStream(zipFileName);
			ZipOutputStream zos = new ZipOutputStream(fos);
			
			for(File originFile : files) {
				
				zos.putNextEntry(new ZipEntry(originFile.getName()));
				
				int BUFFER = 1024;
				FileInputStream fis = new FileInputStream(originFile);
				BufferedInputStream bis = new BufferedInputStream(fis, BUFFER);
				
				byte[] bytes = new byte[BUFFER];
				
				int count;
				while((count = bis.read(bytes, 0, BUFFER)) != -1) {
					zos.write(bytes, 0, count);
				}	
				fis.close();
				zos.closeEntry();
			}
			zos.close();
			return file;
		} catch (Exception e) {
			log.error("Error in convertZip :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	public String getAlertName(int alerttypeid) {
		String alertTName = "";
		switch (alerttypeid) {

		case 1:
			alertTName = "Temperature Alert";
			break;
		case 2:
			alertTName = "Low Battery Alert";
			break;
		case 3:
			alertTName = "Power Loss Alert";
			break;
		case 4:
			alertTName = "Geofence Alert";
			break;
		case 11:
			alertTName = "Device Not Reporting";
			break;
		case 14:
			alertTName = "Humidity Alert";
			break;
		case 17:
			alertTName = "Power Recovery Alert";
			break;
		case 18:
			alertTName = "Air Quality Index Alert";
			break;
		case 19:
			alertTName = "CO2 Alert";
			break;
		case 20:
			alertTName = "Human Motion Detection Alert ";
			break;
		case 21:
			alertTName = "Dog Motion Detection Alert ";
			break;
		case 22:
			alertTName = "Cat Motion Detection Alert ";
			break;
		case 23:
			alertTName = "Dog Sound Alert ";
			break;
		case 24:
			alertTName = "Cat Sound Alert ";
			break;
		case 25:
			alertTName = "Offline Alert";
			break;
		case 26:
			alertTName = "Scheduled Treat Toss Alert";
			break;
		case 27:
			alertTName = "Sound Alert";
			break;
		case 29:
			alertTName = "Water Leakage Alert";
			break;
		case 30:
			alertTName = "Door Alarm Alert";
			break;
		case 31:
			alertTName = "Water Level Alert";
			break;
		case 32:
			alertTName = "Door Reminder Alert";
			break;
		default:
			break;
		}
		return alertTName;
	}

}
