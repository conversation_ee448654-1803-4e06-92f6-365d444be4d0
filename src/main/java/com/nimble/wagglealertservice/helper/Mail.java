package com.nimble.wagglealertservice.helper;

import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.AuthenticationFailedException;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.InternetHeaders;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.nimble.wagglealertservice.Util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class Mail {
	
	private static final Logger log = LogManager.getLogger(Mail.class);

	@Autowired
	private static SecretManagerService secretManagerService;

	@Value("${aws_ses_secret_name}")
	private static String SES_SECRET_NAME;

	public static boolean SendMail_SES (String sub,String toAddr[],String ccA[],String mailmsg, String from_add,String filename)
	{   		
		
		String EMAIL_HOST = "email-smtp.us-west-2.amazonaws.com";
		String EMAIL_HOST_USER = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_access_key");
		String EMAIL_HOST_PASSWORD = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_secret_key");
		//String login = "<EMAIL>";
		String login = "<EMAIL>";
		Transport transport = null;

		try {
			Properties props = new Properties();
			props.put("mail.transport.protocol", "smtps");
			props.put("mail.smtp.auth", "true");
//			props.setProperty("mail.smtp.startSSL.enable", "true");
//			props.setProperty("mail.smtp.port", "25");

			props.setProperty("mail.smtp.port", "587");	           
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
            
    		// Create a Session object to represent a mail session with the specified properties. 
    		Session session = Session.getDefaultInstance(props);

    		// Create a message with the specified information. 
    		MimeMessage msg = new MimeMessage(session);
    		msg.setFrom(new InternetAddress(login));
    		
    		InternetAddress[] toAddress = new InternetAddress[toAddr.length];
    		for( int i = 0; i < toAddr.length; i++ ) {
    			toAddress[i] = new InternetAddress(toAddr[i]);
			}
    		
    		for( int i = 0; i < toAddress.length; i++) {
				msg.addRecipient(Message.RecipientType.TO, toAddress[i]);
			}
    		
//    		msg.setRecipient(Message.RecipientType.TO, new InternetAddress(toAddr));
    		
    		InternetAddress[] ccAddress = new InternetAddress[ccA.length];
			for( int i = 0; i < ccA.length; i++ ) {
				if(ccA[i].trim().isEmpty()) {
					break;
				}
				ccAddress[i] = new InternetAddress(ccA[i]);
			}
			
			for( int i = 0; i < ccAddress.length; i++) {
				if(ccAddress[i] == null) {
					break;
				}
				msg.addRecipient(Message.RecipientType.CC, ccAddress[i]);
			}
			InternetHeaders headers = new InternetHeaders();
			headers.addHeader("Content-type", "text/html; charset=UTF-8");
			
    		msg.setSubject(sub);
    		msg.setContent(mailmsg,"text/html; charset=UTF-8");
    		    		
			// Create the message part
			BodyPart messageBodyPart = new MimeBodyPart(headers, mailmsg.getBytes("UTF-8"));

			// Now set the actual message
//			messageBodyPart.setText(mailmsg);

			// Create a multipar message
			Multipart multipart = new MimeMultipart();

			// Set text message part
			multipart.addBodyPart(messageBodyPart);

			// Part two is attachment
			messageBodyPart = new MimeBodyPart();
			DataSource source = new FileDataSource(filename);
			messageBodyPart.setDataHandler(new DataHandler(source));
			messageBodyPart.setFileName(filename);
			multipart.addBodyPart(messageBodyPart);

			// Send the complete message parts
			msg.setContent(multipart);

    		// Create a transport.        
    		transport = session.getTransport();
    		log.info("Attempting to send an email through the Amazon SES SMTP interface...");

    		// Connect to Amazon SES using the SMTP username and password you specified above.
    		transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);

    		// Send the email.
    		transport.sendMessage(msg, msg.getAllRecipients());
    		
			log.info("Sent message successfully....To-"+ toAddr +"::" );

			return true;
		}
		catch (AuthenticationFailedException ex)
		{
			log.error("Authentication Exception occured :" + ex.getMessage());
		}
		catch (AddressException ex)
		{
			log.error("Address Exception occured :" + ex.getMessage());
		}
		catch (MessagingException ex)
		{
			ex.printStackTrace();
			log.error("Message Exception occured :" + ex.getMessage()); 
		} 
		catch (Exception e) {
			log.error("Error in mail sending : "+e.getLocalizedMessage());
		}
		return false;
	}
}
