package com.nimble.wagglealertservice.configuration;

import java.io.IOException;
import java.util.Properties;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import com.nimble.wagglealertservice.quartz.JobsListener;
import com.nimble.wagglealertservice.quartz.TriggerListner;

 
@Configuration
public class QuartzSchedulerConfig {
 
	@Autowired
	DataSource irisDataSource;

	@Autowired
	private ApplicationContext applicationContext;

	@Autowired
	private TriggerListner triggerListner;

	@Autowired
	private JobsListener jobsListener;

	/**
	 * create scheduler
	 */
	@Bean
	public SchedulerFactoryBean schedulerFactoryBean() throws IOException {

		SchedulerFactoryBean factory = new SchedulerFactoryBean();
		factory.setOverwriteExistingJobs(true);
		factory.setDataSource(irisDataSource);
		factory.setQuartzProperties(quartzProperties());

		// Register listeners to get notification on Trigger misfire etc
		factory.setGlobalTriggerListeners(triggerListner);
		factory.setGlobalJobListeners(jobsListener);

		AutowiringSpringBeanJobFactory jobFactory = new AutowiringSpringBeanJobFactory();
		jobFactory.setApplicationContext(applicationContext);
		factory.setJobFactory(jobFactory);

		return factory;
	}

	/**
	 * Configure quartz using properties file
	 */
	@Bean
	public Properties quartzProperties() throws IOException {
		PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
		propertiesFactoryBean.setLocation(new ClassPathResource("/quartz.properties"));
		propertiesFactoryBean.afterPropertiesSet();
		return propertiesFactoryBean.getObject();
	}
 
  
}