package com.nimble.wagglealertservice.quartz;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.JobListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.nimble.wagglealertservice.service.IScheduleAlertService;

@Component
public class JobsListener implements JobListener {
	
	@Autowired
	IScheduleAlertService scheduleService;
	
	private static final Logger log = LogManager.getLogger(JobsListener.class);

	@Override
	public String getName() {
		return "globalJob";
	}

	@Override
	public void jobToBeExecuted(JobExecutionContext context) {
		log.info(context.getJobDetail().getJobDataMap().get("userid"));
		log.info(">>>>>>>>>>>>>>>>>>>>> JobsListener.jobToBeExecuted()");
	}

	@Override
	public void jobExecutionVetoed(JobExecutionContext context) {
		log.info(">>>>>>>>>>>>>>>>>>>>>JobsListener.jobExecutionVetoed()");
//		JobKey key = context.getJobDetail().getKey();
//		boolean check = remainderservice.disableReminderDetails(key.getGroup(), key.getName(), "pending", 0);

	}

	@Override
	public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
		log.info(">>>>>>>>>>>>>>>>>>>>> JobsListener.jobWasExecuted()");
		JobKey key = context.getJobDetail().getKey();
		if(key.getGroup().equalsIgnoreCase("PLPBAlert")) {
			scheduleService.updateAlertByName(key.getName(),0);
			log.info(key.getName() + " updated");
		}
		
	}

}
