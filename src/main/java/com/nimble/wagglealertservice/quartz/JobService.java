package com.nimble.wagglealertservice.quartz;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.scheduling.quartz.QuartzJobBean;

public interface JobService {
	boolean scheduleOneTimeJob(String jobName, Class<? extends QuartzJobBean> jobClass, Date date, String userId);

	boolean scheduleRepeatJob(String jobName, Class<? extends QuartzJobBean> jobClass, Date date, String userid, String repeatedTime, int intervel);

	boolean updateOneTimeJob(String jobName, Date date, String userId);

	boolean updateRepeatJob(String jobName, Date date, String userId, String repeatedType, int intervel);

	boolean unScheduleJob(String jobName);

	boolean deleteJob(String jobName, String userId);

	boolean pauseJob(String jobName);

	boolean resumeJob(String jobName);

	boolean startJobNow(String jobName);

	boolean isJobRunning(String jobName, String userId);

	List<Map<String, Object>> getAllJobs(String userId, String remainder);

	boolean isJobWithNamePresent(String jobName, String userId);

	String getJobState(String jobName, String userId);

	boolean stopJob(String jobName);
	
	public boolean scheduleTempAlertRepeatJobs(String jobName,Class<? extends QuartzJobBean> jobClass, Date dailyDate) ;

}
