package com.nimble.wagglealertservice.job;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.InterruptableJob;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.nimble.wagglealertservice.helper.Helper;
import com.nimble.wagglealertservice.quartz.JobService;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.IFirebaseService;
import com.nimble.wagglealertservice.service.INodeService;
import com.nimble.wagglealertservice.service.IScheduleAlertService;
import com.nimble.wagglealertservice.service.IUserServiceV4;

public class NotReportingAlert extends QuartzJobBean implements InterruptableJob {

	private static final Logger log = LogManager.getLogger(NotReportingAlert.class);

	@Autowired
	JobService jobService;

	@Autowired
	IUserServiceV4 userserviceV4;

	@Autowired
	IAlertServiceV4 alertServiceV4;

	@Autowired
	IDeviceStatusService deviceStatusService;
	
	@Autowired
	IFirebaseService iFirebaseService;

	@Autowired
	INodeService nodeService;

	Helper helper = new Helper();
	
	@Value("${irisservice_url}")
	private String irisservice_url;
	
	@Value("${validate_auth}")
	private String validate_auth;
	
	@Autowired
	IScheduleAlertService scheduleAlert;

	@Override
	protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {

		DeviceStatusNotify deviceStatusNotify = new DeviceStatusNotify();

		log.info("NotReporting Job started...");
		
		JobKey jobKey = jobExecutionContext.getJobDetail().getKey();
		String jobName = jobKey.getName();

		if (jobName.equalsIgnoreCase("NotReporting")) {
			String ip = irisservice_url.trim();// get ip
			long credit_points[] = userserviceV4.getCreditPoints();// get creditpoints

			deviceStatusNotify.notifyGateway(deviceStatusService, nodeService, userserviceV4, alertServiceV4,
					scheduleAlert, iFirebaseService,
					 ip, credit_points, validate_auth.trim());
			deviceStatusNotify.notifyNode(deviceStatusService, nodeService, userserviceV4, alertServiceV4,
					scheduleAlert, iFirebaseService,
					 ip, credit_points, validate_auth.trim());
		}
		log.info("NotReporting Job completed");
	}

	@Override
	public void interrupt() throws UnableToInterruptJobException {
		log.info("Stopping thread... ");
	}
}
