package com.nimble.wagglealertservice.job;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.InterruptableJob;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Trigger;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.nimble.wagglealertservice.helper.Helper;
import com.nimble.wagglealertservice.helper.Mail;
import com.nimble.wagglealertservice.pojo.JAlertCountReport;
import com.nimble.wagglealertservice.pojo.JTempRangeReport;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.IScheduleAlertService;
import com.nimble.wagglealertservice.service.IUserServiceV4;

public class TempAlert extends QuartzJobBean implements InterruptableJob {

	private static final Logger log = LogManager.getLogger(TempAlert.class);

	@Autowired
	IUserServiceV4 userserviceV4;

	@Autowired
	IScheduleAlertService scheduleAlert;

	@Autowired
	IAlertServiceV4 alertServiceV4;

	@Autowired
	IDeviceStatusService deviceStatusService;
	
	@Value("${irisservice_url}")
	private String irisservice_url;
	
	@Value("${validate_auth}")
	private String validate_auth;
	
	@Value("${to_mail}")
	private String to_mail;
	
	@Value("${cc_mail}")
	private String cc_mail;
	
	@Value("${report_mail_from_add}")
	private String report_mail_from_add;

	@Override
	protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {

		log.info("Temp Alert begins...");
		
		JobKey key = jobExecutionContext.getJobDetail().getKey();
		Trigger trigger = jobExecutionContext.getTrigger();
		
		log.info("Temp Alert Job started with key :" + key.getName() + ", Group :" + key.getGroup() + " , Thread Name :"
				+ Thread.currentThread().getName() + " ,Time now :" + new Date());
		
		JobDataMap dataMap = jobExecutionContext.getMergedJobDataMap();
		Date triggerDate = jobExecutionContext.getScheduledFireTime();

		String repeatType = trigger.getJobDataMap().getString("repeattype");

		Calendar fromCal = Calendar.getInstance();
		fromCal.setTime(triggerDate); 
		
		if(repeatType.contains("Day")) {			
			fromCal.add(Calendar.DATE, -1);
		}
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

		String fromDt = formatter.format(fromCal.getTime());
		Helper _helper = new Helper();
		
		ArrayList<File> files = new ArrayList<File>();
		File zipFile = null;
		
		ArrayList<JTempRangeReport> rptList = alertServiceV4.getTempRangeReport(fromDt);
		File file1 = new Helper().writeCSVDataLineByLine(rptList,fromDt);
		
		if(file1 != null)
			files.add(file1);
		
		ArrayList<JAlertCountReport> countList = alertServiceV4.getAlertCountReport(fromDt);
		File file2 = new Helper().writeAlertCountCSVDataLineByLine(countList, fromDt);
		
		if(file2 != null)
			files.add(file2);
		
		String fileName = "TempRangeReport&AlertCountReport_" + fromDt;
		
		zipFile = _helper.convertMultipleFilesToZip(files, fileName);
		
		if( zipFile == null ) {
			_helper.deleteMultipeFiles(files);
		}
		
		String[] ccAddress = cc_mail.split(",");

		String mailSubject = "Alert Count and Temp Range Report-"+fromDt;
		String mailContent = "<p> Please find the reports in the attachements "
				+ "<li>List of customers temperature alert range is nearer to their temperature settings</li>"
				+ "<li>List of gateways sending more than 5 alerts per day</li></p>";
		
		String[] toAddress = to_mail.split(",");
		
		boolean is_mail_sent = Mail.SendMail_SES(mailSubject, toAddress, ccAddress, mailContent, report_mail_from_add, zipFile.getName());
		
		if(!is_mail_sent) {
			log.info("mail not sent :: email : "+to_mail);
		}
		
		_helper.deleteMultipeFiles(files);
		_helper.deleteFile(zipFile);
	}

	@Override
	public void interrupt() throws UnableToInterruptJobException {
		log.info("CD Team Alert - Stopping thread... ");
	}
}
