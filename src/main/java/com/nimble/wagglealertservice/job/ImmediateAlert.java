package com.nimble.wagglealertservice.job;

import java.util.ArrayList;
import java.util.List;

import org.apache.http.entity.StringEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.google.gson.Gson;
import com.nimble.wagglealertservice.Util.RemainderservicesUtil;
import com.nimble.wagglealertservice.dto.JDailyAlertTypeCount;
import com.nimble.wagglealertservice.dto.JFcmNotification;
import com.nimble.wagglealertservice.dto.JGateway;
import com.nimble.wagglealertservice.dto.JUserFeatureCount;
import com.nimble.wagglealertservice.entity.JScheduleAlert;
import com.nimble.wagglealertservice.helper.Helper;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.IFirebaseService;
import com.nimble.wagglealertservice.service.IScheduleAlertService;
import com.nimble.wagglealertservice.service.IUserServiceV4;
import com.nimble.wagglealertservice.service.SMSInterface;
import com.nimble.wagglealertservice.service.impl.Plivo;

@Configuration
public class ImmediateAlert {

	private static final Logger log = LogManager.getLogger(ImmediateAlert.class);

	@Autowired
	IUserServiceV4 userserviceV4;

	@Autowired
	IAlertServiceV4 alertServiceV4;

	@Autowired
	IDeviceStatusService deviceStatusService;

	@Autowired
	IScheduleAlertService scheduleAlert;
	
	@Autowired
	IFirebaseService iFirebaseService;
	
	public ImmediateAlert(IUserServiceV4 userserviceV4, IAlertServiceV4 alertServiceV4,
			IDeviceStatusService deviceStatusService, IScheduleAlertService scheduleAlert,
			IFirebaseService iFirebaseService) {
		super();
		this.userserviceV4 = userserviceV4;
		this.alertServiceV4 = alertServiceV4;
		this.deviceStatusService = deviceStatusService;
		this.scheduleAlert = scheduleAlert;
		this.iFirebaseService = iFirebaseService;
	}

	public boolean sendInstantAlert(JScheduleAlert jAlert, String scheduler, String server_ip, String validate_auth) {

		log.info("sendInstantAlert begins...");
		
		String createdAt = RemainderservicesUtil.getCurrentTimeUTC();
		String updateAt = createdAt;

		jAlert.setCreated_at(createdAt);
		jAlert.setUpdated_at(updateAt);
		jAlert.setEnable(1);
		
		String schedulerName = jAlert.getScheduler_name();

		SMSInterface smsGateway = null;
		boolean sentSmsMsg = false;
		boolean sentEmailMsg = false;
		boolean appNotifySent = false;
		boolean updateStatus = false;
		String sentSmsMsgString = "NA";
		String ip = server_ip;

		long alertId = jAlert.getAlert_id();
		long alertCfgId = jAlert.getAlertcfg_id();
		long gatewayId = jAlert.getGateway_id();
		//int notifyFreq = jAlert.getNotify_freq();
		String gatewayname = jAlert.getGateway_name();
		String smsMsg = jAlert.getSms_msg();
		String mobileNos = jAlert.getMobilenos();
		String emailMsg = jAlert.getEmail_msg();
		String app_msg = jAlert.getApp_msg();
		String email = jAlert.getEmail();
		int cmp_id = (int) jAlert.getCmp_id();
		String cmpName = jAlert.getCmp_name();
		String timeZone = jAlert.getTimezone();
		long alerttypeId = jAlert.getAlerttype_id();
		boolean alert_enable = jAlert.isAlert_enable();
		float alertValue = jAlert.getAlertvalue();
		String pkt_date = jAlert.getPkt_datetime();
		String planVersion = jAlert.getPlan_ver();
		String featureCodeSMS = jAlert.getFeaturecode_SMS();
		String featureCodeEmail = jAlert.getFeaturecode_MAIL();
		String featureCodeAlert = jAlert.getFeaturecode_alert();
		String featureCodeNotify = jAlert.getFeaturecode_NOTIFY();
		float minval = jAlert.getMinval();
		float maxval = jAlert.getMaxval();
		String notificationtype = jAlert.getNotificationtype();

		log.info("Start Time : " + pkt_date);

		try {

			smsGateway = new Plivo();
			JGateway asset = new JGateway();
			int smsCount = 0;
			int emailCount = 0;
			int notifyCount = 0;
			log.info("alert_enable : " + alert_enable);

			if (alert_enable) {

				if (!mobileNos.equalsIgnoreCase("na") && !smsMsg.equalsIgnoreCase("na")) {
					sentSmsMsgString = smsGateway.callIrisSmsAPIReturnString(mobileNos, smsMsg, cmp_id, cmpName, server_ip,validate_auth);


					if(!sentSmsMsgString.equalsIgnoreCase("NA")) {
						sentSmsMsg = true;
					}

					if(sentSmsMsg)
						smsCount = mobileNos.split(",").length;
					log.info("SmsMsg :" + mobileNos + " : " + sentSmsMsg);
				}

				if (!email.equalsIgnoreCase("na") && !emailMsg.equalsIgnoreCase("na")) {
					String emailMsgSub = emailMsg.split(",")[0];
					String emailSub = emailMsgSub;
					sentEmailMsg = smsGateway.callIrisEmailAPI(email, emailSub, emailMsg, ip, true,validate_auth);
					
					if(sentEmailMsg)
						emailCount = email.split(",").length;
					log.info("EmailMsg : " + email + " : " + sentEmailMsg);
				}

				String alert_msg = smsMsg;
				if(alert_msg.equalsIgnoreCase("NA"))
					alert_msg = emailMsg;

//				AlertCfgV4 alertcfg = deviceStatusService.getAlertCfg(gatewayId, cmp_id,alerttypeId);
//				String notifytype = alertcfg != null ? alertcfg.getNotificationtype() : "";
//				boolean app_notify = false;
//				
//				if(notifytype.length()==4) {
//					if (notifytype.charAt(3) == '1') 
//						app_notify = true;
//				}
				//userserviceV4.getAppNotifyEnable(cmp_id)
				if (!featureCodeNotify.equalsIgnoreCase("NA") && !app_msg.equalsIgnoreCase("NA")) {
					log.info("PUSH notification enabled for this company id- " + cmp_id);

//                	boolean bExtraCredits =false;
//                    long avlCredits =  asset.getTotalAssignedCredits()-asset.getTotalCreditSpent();
//                    if(avlCredits <=0){
//                       avlCredits = asset.getTotAssigExCredits()-asset.getTotalExtraCreditsSpent();
//                       bExtraCredits = true;
//                    }
					String alertTName = new Helper().getAlertName((int)alerttypeId);
					
					log.info("Do App Notification" + "Gatewayid: " + gatewayId);
					List<String> tokens = userserviceV4.getTokensByGatewayId(gatewayId);
					
//                    	if(smsMsg.contains("deg"))
//                    		alert_msg = alert_msg +" : "+getAlertValue(alertValue,smsMsg);
//                    	else if(alertValueUnit.equalsIgnoreCase("%"))
//                    		alert_msg = alertName +" : "+getAlertValue((int)alertValue,alertValueUnit);

					if (tokens != null && tokens.size() > 0) {
						long monitortype= deviceStatusService.getMonitortype(gatewayId);
						boolean isThreadSensor = deviceStatusService.checkIsThreadSensor(gatewayId);
						long hubId = deviceStatusService.getHubIdForSensor(gatewayId);
						String source ="alert";
						
						if(monitortype == 9)
							 source ="sensorAlert";

						if(monitortype == 9 && isThreadSensor)
							source ="sensorThreadAlert";

						JFcmNotification fcmNotification = new JFcmNotification(alertTName, app_msg, "NA", "NA", tokens, 
								source, "NA");
						log.info("fcmNotification: "+alertTName);
						fcmNotification.setGatewayId(gatewayId);
						fcmNotification.setHubId(hubId);

						int message = iFirebaseService.sendNotification(fcmNotification);
						log.info(message + " message(s) were sent");						
						if(message > 0) {
							notifyCount = message;
							appNotifySent = true;
						}
					}
				} else {
					log.info("App notify is not enabled");
				}
			}
			
			String notifiedTime = "1753-01-01 00:00:00";
			String alertMsg = "NA";
			if ((sentSmsMsg || sentEmailMsg || appNotifySent)) {
				notifiedTime = pkt_date;
				
				if(!smsMsg.equalsIgnoreCase("NA"))
					alertMsg = smsMsg;
				else if(!emailMsg.equalsIgnoreCase("NA"))
					alertMsg = emailMsg;
				else
					alertMsg = app_msg;
			}
			
			if (scheduler.equalsIgnoreCase("PLPB")) {
				int n_sms=0;
				int n_mail=0;
				int n_notify=0;
				
				if(sentSmsMsg)
					n_sms= 1;
				if(sentEmailMsg)
					n_mail =1;
				if(appNotifySent)
					n_notify =1;
				
				boolean insertAlert = alertServiceV4.InsertAlert(pkt_date, pkt_date, timeZone, alertValue, "NA",
						0.0f, "NA", 0.0f, "NA", "NA", "NA", 0.0f, 0, "NA", 0.0f, 0.0f, 0.0f, 0.0f, "NA", 0, alerttypeId,
						gatewayId, gatewayId, alertCfgId, cmp_id,notifiedTime,notifiedTime,alertMsg,1,n_sms,n_mail,n_notify,
						minval,maxval,notificationtype,sentSmsMsgString);
				
				alertServiceV4.insertAlertStatus("insert", cmp_id, gatewayId, alerttypeId, true);
				
				log.info("insertAlert : " + insertAlert);
				log.info("alerttype_id : " + alerttypeId + ", gateway_id : " + gatewayId + ", cmp_id : " + cmp_id);

				if (insertAlert) {
					alertId = alertServiceV4.getAlertId(pkt_date, alertCfgId);
					log.info("Got AlertId : " + alertId);

					updateStatus = scheduleAlert.updateScheduleAlert(alertId, schedulerName);
					log.info("Update alertId in schedule_alert : " + updateStatus);

					updateStatus = alertServiceV4.updateplByAlertId(alertId, schedulerName);
					log.info("updateplByAlertId :: schedulername : " + schedulerName + " : " + updateStatus);
				}
			}
			
			if ((sentSmsMsg || sentEmailMsg || appNotifySent) && alertId != 0) {
				
				int sms=0;
				int mail=0;
				int notify=0;
				
				if(sentSmsMsg)
					sms = 1;
				if(sentEmailMsg)
					mail = 1;
				if(appNotifySent)
					notify = 1;
				
				if (!scheduler.equalsIgnoreCase("PLPB")) {
					alertServiceV4.UpdateAlertNotified(alertId, notifiedTime, alertMsg,sms,mail,notify,sentSmsMsgString);
				}
		
				if (sentEmailMsg)
					asset.setEmailcnt(2);

				if (sentSmsMsg)
					asset.setSmscnt(2);
				
				if(appNotifySent)
					asset.setPushnotificationcnt(2);

				updateStatus = deviceStatusService.updateThrottlingCount(gatewayId, gatewayname, cmp_id, asset);
				log.info("updateThrottlingCount : " + updateStatus);

				// to update alert type wise notification count	
				if(emailCount >0 || smsCount>0 || notifyCount>0) {
					JDailyAlertTypeCount datCount = new JDailyAlertTypeCount(gatewayId, cmp_id, alerttypeId, emailCount, smsCount, notifyCount);
					boolean stat = deviceStatusService.updateDailyAlertCount(datCount);
				
					log.info("updateDailyAlertCount: "+stat);
				}
				if (alerttypeId == 17) {
					updateStatus = alertServiceV4.UpdateAlert(gatewayId);
					log.info("Update PLPBAlert for delete : " + updateStatus);
				}

				if (!planVersion.equalsIgnoreCase("v1")) {
					ArrayList<JUserFeatureCount> ufcList = new ArrayList<JUserFeatureCount>();
					long userId = deviceStatusService.getUserGateway(gatewayId);
					
					if(sentEmailMsg) {
						JUserFeatureCount ufcObj = new JUserFeatureCount(gatewayId,userId, featureCodeEmail, emailCount);
						ufcList.add(ufcObj);
					}
					
					if(appNotifySent) {
						JUserFeatureCount ufcObj = new JUserFeatureCount(gatewayId,userId, featureCodeNotify, notifyCount);
						ufcList.add(ufcObj);
					}
					
					if(sentSmsMsg) {
						JUserFeatureCount ufcObj = new JUserFeatureCount(gatewayId,userId, featureCodeSMS, smsCount);
						ufcList.add(ufcObj);
					}
					
					if(sentEmailMsg || sentSmsMsg || appNotifySent) {
						JUserFeatureCount ufcObj = new JUserFeatureCount(gatewayId,userId, featureCodeAlert, 1);
						ufcList.add(ufcObj);
					}
					
					if (!ufcList.isEmpty()) {
						String url = ip + "/wgtxnsvc/v4.0/updatefeaturecredit?";
						Gson gson = new Gson();
						String json = gson.toJson(ufcList);

						StringEntity postingString = new StringEntity(json);
						
						boolean updateFeatureSMS = smsGateway.postAPIWithAuthHeader(url, validate_auth, postingString);
						log.info("updateFeatureCredit : " + updateFeatureSMS);
					}
					//updateFeatureCredit(deviceStatusService, gatewayId, featureCodeSMS, featureCodeEmail, sentSmsMsg, sentEmailMsg, smsCount, emailCount, ip, validate_auth,featureCodeAlert);
				}

			} else {
				log.info("alert not sent");

				updateStatus = alertServiceV4.DeleteplAlertHistoryStatus(alertId);
				log.info("Delete plAlertHistoryStatus : " + updateStatus);
			}
			
			return true;
		} catch (Exception e) {
			log.error("Error occurred : PowerLossAlert : " + e.getLocalizedMessage());
			return false;
		} 
	}

	
//	private static String getAlertValue(float alertValue, String unit) {
//		if (alertValue <= -100000 || alertValue >= 100000)
//			return "Error";
//		if (unit.equalsIgnoreCase("deg F"))
//			return CelsiusToFahrenheit(alertValue) + " " + unit;
//		return alertValue + " " + unit;
//	}

	public static float CelsiusToFahrenheit(float tempmvalIndegreeCelsius) {
		double degreeFahrenheit = Double.valueOf(tempmvalIndegreeCelsius).floatValue() * 9 / 5 + 32;
		double roundvalues = Math.round(degreeFahrenheit * 100.0) / 100.0;
		return (float) roundvalues;
	}
	
//	public static void updateFeatureCredit(IDeviceStatusService deviceStatusService, long gatewayId, String featureCodeSMS, String featureCodeEmail,
//			boolean sms_sent, boolean mail_sent, int smsCount, int mailCount, String ip, String validate_auth, String featureCodeAlert) {
//		SMSInterface smsGateway = new Plivo();
//		
//		long userId = deviceStatusService.getUserGateway(gatewayId);
//		if (userId != 0) {
//			String url = "";
//			log.info("userid : " + userId);
//			if (sms_sent) {
//				url = ip + "/wgtxnsvc/v4.0/updatefeaturecredit?featurecode="
//						+ featureCodeSMS + "&userid=" + userId + "&cnt=" + smsCount;
//				boolean updateFeatureSMS = smsGateway.postAPIWithAuthHeader(url, validate_auth);
//				log.info("updateFeatureSMS : " + updateFeatureSMS);
//			}
//			if (mail_sent) {
//				url = ip + "/wgtxnsvc/v4.0/updatefeaturecredit?featurecode="
//						+ featureCodeEmail + "&userid=" + userId + "&cnt=" + mailCount;
//				boolean updateFeatureEmail = smsGateway.postAPIWithAuthHeader(url, validate_auth);
//				log.info("updateFeatureEmail : " + updateFeatureEmail);
//			}
//			
//			if((sms_sent || mail_sent) && !featureCodeAlert.equalsIgnoreCase("NA")) {
//				url = ip + "/wgtxnsvc/v4.0/updatefeaturecredit?featurecode="
//						+ featureCodeAlert + "&userid=" + userId + "&cnt=1";
//				boolean updateFeatureAlert = smsGateway.postAPIWithAuthHeader(url, validate_auth);
//				log.info("updateFeatureEmail : " + updateFeatureAlert);
//			}
//		}
//	}
	
	public static void updateFeatureCredit(IDeviceStatusService deviceStatusService,
			 String ip, String validate_auth, ArrayList<JUserFeatureCount> ufcList) {
		try {
		SMSInterface smsGateway = new Plivo();
		
		if (!ufcList.isEmpty()) {
			String url = ip + "/wgtxnsvc/v4.0/updatefeaturecredit?";
			Gson gson = new Gson();
			String json = gson.toJson(ufcList);

			StringEntity postingString = new StringEntity(json);
			
			boolean updateFeatureSMS = smsGateway.postAPIWithAuthHeader(url, validate_auth, postingString);
			log.info("updateFeatureCredit : " + updateFeatureSMS);
		}
		}catch (Exception e) {
			log.error("updateFeatureCredit:"+e.getLocalizedMessage());
		}
	}
}
