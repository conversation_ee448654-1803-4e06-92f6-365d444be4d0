package com.nimble.wagglealertservice.job;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.InterruptableJob;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.nimble.wagglealertservice.helper.Helper;
import com.nimble.wagglealertservice.quartz.JobService;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.INodeService;

public class ExecuteNotify extends QuartzJobBean implements InterruptableJob {

	private static final Logger log = LogManager.getLogger(ExecuteNotify.class);

	@Autowired
	JobService jobService;

	@Autowired
	IDeviceStatusService deviceStatusService;
	
	@Autowired
	IAlertServiceV4 alertServiceV4;

	@Autowired
	INodeService nodeService;

	Helper helper = new Helper();

	@Override
	protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {

		log.info("Ackalerts Job started...");

		JobKey jobKey = jobExecutionContext.getJobDetail().getKey();
		String jobName = jobKey.getName();

		if (jobName.equalsIgnoreCase("Ackalerts")) {
			alertServiceV4.ackExpiredAlerts(0L);// acknowledge alerts
		}
		log.info("Ackalerts Job completed");
	}

	@Override
	public void interrupt() throws UnableToInterruptJobException {
		log.info("Stopping thread... ");
	}
}
