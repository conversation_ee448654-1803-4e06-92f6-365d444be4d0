package com.nimble.wagglealertservice.job;

import java.lang.reflect.Type;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.TimeZone;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.nimble.wagglealertservice.Util.RemainderservicesUtil;
import com.nimble.wagglealertservice.dto.AlertCfgV4;
import com.nimble.wagglealertservice.dto.FeatureCredit;
import com.nimble.wagglealertservice.dto.JAlert;
import com.nimble.wagglealertservice.dto.JGateway;
import com.nimble.wagglealertservice.dto.JLastGatewayRpt;
import com.nimble.wagglealertservice.dto.JLastNodeRpt;
import com.nimble.wagglealertservice.dto.JNode;
import com.nimble.wagglealertservice.dto.JUser;
import com.nimble.wagglealertservice.entity.JScheduleAlert;
import com.nimble.wagglealertservice.helper.Helper;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.IFirebaseService;
import com.nimble.wagglealertservice.service.INodeService;
import com.nimble.wagglealertservice.service.IScheduleAlertService;
import com.nimble.wagglealertservice.service.IUserServiceV4;
import com.nimble.wagglealertservice.service.SMSInterface;
import com.nimble.wagglealertservice.service.impl.Plivo;

@Component
public class DeviceStatusNotify {

	private static final Logger log = LogManager.getLogger(DeviceStatusNotify.class);

	Helper helper = new Helper();

	private static final long ONE_HOUR = 1 * 60 * 60 * 1000;

	public static final int NOT_REPORTING = 0;

	public boolean notifyGateway(IDeviceStatusService deviceStatusService, INodeService nodeService,
			IUserServiceV4 userserviceV4, IAlertServiceV4 alertServiceV4,IScheduleAlertService scheduleAlert,
			IFirebaseService iFirebaseService, String ip, long[] credit_points, String validate_auth) {
		log.info("Entering notifyGateway:: ");
		long companyId = 0;
		long gatewayId = 0;
		String gatewayName = null;
		String nodeName = null;
		long nodeId = -1;
		long notreportInterval = 3600000; // (1 hour)
//		String gatewayname = "Gateway";
		String actualGatewayName = "";
		int rowCount = 1;
		boolean check = true;
		int count = 0;
//		String updateGateway = "";
		boolean isEnable = false;
		String cmpName = "";
		try {
			while (check) {
				ArrayList<JLastGatewayRpt> gatewayList = deviceStatusService.getLastGatewayReport(count);
				count += 1000;
				if (gatewayList == null || gatewayList.isEmpty()) {
					log.info("notify gateway completed");
					return true;
				}

				for (JLastGatewayRpt lgr : gatewayList) {
					Timestamp dateTime = lgr.getDateTime();
					String timeZone = lgr.getTimeZone();
					gatewayId = lgr.getGatewayId();
					notreportInterval = lgr.getNotReporting();
					Timestamp lastDateTime = dateTime;
					gatewayName = lgr.getGatewayName();
					actualGatewayName = lgr.getActualGatewayName();
					companyId = lgr.getCompanyId();
					isEnable = lgr.isEnable();
					cmpName = lgr.getCmpName();
					String currDateTime = Helper.getCurrentDateTime(timeZone);

					log.info(rowCount + ": gatewayId : " + gatewayId);

					if ((dateTime != null) && (gatewayId >= 0)) {

						long curTimeMillis = System.currentTimeMillis();
						long rptTimeMillis = Helper.getTimestampInMillis(dateTime, "GMT" + timeZone);

						long diff = curTimeMillis - rptTimeMillis;

						long reportIntervalinMillis = notreportInterval * 60 * 1000;

						if (diff >= reportIntervalinMillis) {
							log.info("Report time exceeded " + diff + " > " + reportIntervalinMillis);

							deviceStatusService.updateGatewayStatus1(gatewayId, NOT_REPORTING);

							boolean isNRAlert = deviceStatusService.isNRAlert(companyId, currDateTime, gatewayId);

							log.info("isNRAlert : " + isNRAlert);
							if (isEnable && isNRAlert) {// isGatewayEnabled() removed
								updateNotReportingAlert(deviceStatusService, userserviceV4, alertServiceV4,
										scheduleAlert, iFirebaseService,
										companyId, gatewayId, gatewayId, nodeId, actualGatewayName,
										nodeName, gatewayName, null, timeZone, lastDateTime, credit_points, ip,
										validate_auth,cmpName);

							}
						} else {
							log.info("Report time not exceeded. Skipped gatewayId : " + gatewayId);
						}
					}
					rowCount++;
				}
				log.info("updated gateways..");
			}
		} catch (Exception e) {
			log.error("Exception occurred on notifygateway : " + e.getLocalizedMessage());
		}
		return true;
	}

	private void updateNotReportingAlert(IDeviceStatusService deviceStatusService,
			IUserServiceV4 userserviceV4, IAlertServiceV4 alertServiceV4,IScheduleAlertService scheduleAlert,
			IFirebaseService iFirebaseService, long companyId, long assetId, long gatewayId, long nodeId,
			String gatewayName, String nodeName, String gatewayname, String optGatewayName, String timeZone,
			Timestamp lastDateTime, long[] credit_points, String ip, String validate_auth,String cmpName) {

		log.info("updateNotReportingAlert begins");
		int count = 0;
		String startDateTime = null;
		String endDateTime = null;
		long alertCfgId = 0;
		long alertId = 0;
		int notifyCount = -1;
		int severity = 0;
		String mobileNos = "NA ";

		try {

			AlertCfgV4 alertCfg = deviceStatusService.getAlertCfg(assetId, companyId,11L);

			if (alertCfg != null) {
				alertCfgId = alertCfg.getId();
				notifyCount = alertCfg.getIntermittentfreq();
				severity = alertCfg.getSeverity();
				mobileNos = alertCfg.getMobilenos();
				startDateTime = Helper.getCurrentDateTime(timeZone);
				endDateTime = Helper.getCurrentDateTime(timeZone);

				JAlert alert = deviceStatusService.getAlert(alertCfgId, assetId);

				if (alert != null) {
					alertId = alert.getId();
					count = alert.getCount();
					startDateTime = alert.getStartdatetime();

					log.info("alert: id " + alertId + ", count " + count);

					deviceStatusService.updateAlert(alertCfgId, endDateTime, count, assetId);

					log.info(assetId + " : " + "updateNRAlert: 1 row updated in alerts table!");
				} else {

					boolean insertAlert = deviceStatusService.insertAlert(startDateTime, endDateTime, timeZone, 0.0f,
							"NA", 0.0f, "NA", 0.0f, "NA", "NA", "NA", 0.0f, 0, "NA", 0.0f, 0.0f, 0.0f, 0.0f, "NA", 0,
							11, assetId, gatewayId, nodeId, alertCfgId, companyId);
					log.info(insertAlert);
					alertId = deviceStatusService.getAlertId(startDateTime, alertCfgId);
					log.info("alertId : " + alertId);
					count++;
					startDateTime = lastDateTime.toString();
				}

				/* notify even if you just inserted the alert */
				if (notifyCount == count) {
					log.info(notifyCount + " == " + count);
					
					long[] creditsInfo;
					creditsInfo = deviceStatusService.getCreditsInfoForGateway((int) gatewayId, (int) companyId);
					JGateway asset = new JGateway();
					asset.setTotalCreditSpent(creditsInfo[0]);// Sum of Credits spent by all gateways of this company
					asset.setTotalAssignedCredits(creditsInfo[1]);// Sum of Credits assigned to all gateways of this company
					asset.setTotalExtraCreditsSpent(creditsInfo[2]);
					asset.setTotAssigExCredits(creditsInfo[3]);

					SMSInterface smsGateway = new Plivo();
					JUser jUser = userserviceV4.getUserPlanVersion(gatewayId);
					String planVer = jUser.getPlan_ver();
					HashMap<String, FeatureCredit> alertAvailList = new HashMap<String, FeatureCredit>();
					Gson gson = new Gson();

					boolean mail_credit = false;
					boolean sms_credit = false;
					boolean notify_credit = false;
					boolean alert_credit = false;

					String featurecode_MAIL = "N_NETWORK_MAIL";
					String featurecode_SMS = "N_NETWORK_SMS";
					String featurecode_NOTIFY = "N_NETWORK_NOTIFY";
					String featurecode_ALERT = "N_NETWORK_COUNT";
					int sms_count = 0;
					int mail_count = 0;
					int alert_count = 0;
					int notify_count = 0;
					String sms_msg= "NA";
					String email_msg = "NA";
					String app_msg = "NA";
					String notificationtype = alertCfg.getNotificationtype();
					
					boolean SMS = false, EMAIL = false, NOTIFY = false;

					if (notificationtype.charAt(0) == '1') {
						SMS = true;
					}
					
					if (notificationtype.charAt(1) == '1') {
						EMAIL = true;
					}
					
					if (notificationtype.charAt(3) == '1') {
						NOTIFY = true;
					}
					
					if (!planVer.equalsIgnoreCase("V1")) {

						String sub_url = ip + "/wgtxnsvc/v4.0/listfeaturecredit?gatewayid=" + assetId;
						String respstatus = smsGateway.callAPIGetMethod(sub_url, validate_auth);

						JSONObject json1 = new JSONObject(respstatus);

						log.info("json1 : " + json1);
						JSONObject resp = json1.getJSONObject("response");
						int stat1 = resp.getInt("Status");

						if (stat1 > 0) {
							JSONArray resp1 = resp.getJSONArray("alertCredits");

							Type type = new TypeToken<ArrayList<FeatureCredit>>() {
							}.getType();
							ArrayList<FeatureCredit> lista = gson.fromJson(resp1.toString(), type);

							for (FeatureCredit n : lista) {
								alertAvailList.put(n.getFeature_code(), n);
							}
						}

						if (!alertAvailList.isEmpty()) {

							mail_count = alertAvailList.containsKey(featurecode_MAIL)
									? (int) alertAvailList.get(featurecode_MAIL).getLimit()
									: 0;
							mail_credit = mail_count > 0 ? true : false;

							sms_count = alertAvailList.containsKey(featurecode_SMS)
									? (int) alertAvailList.get(featurecode_SMS).getLimit()
									: 0;
							sms_credit = sms_count > 0 ? true : false;

							alert_count = alertAvailList.containsKey(featurecode_ALERT)
									? (int) alertAvailList.get(featurecode_ALERT).getLimit()
									: 0;
							alert_credit = alert_count > 0 ? true : false;

							notify_count = alertAvailList.containsKey(featurecode_NOTIFY)
									? (int) alertAvailList.get(featurecode_NOTIFY).getLimit()
									: 0;
							notify_credit = notify_count > 0 ? true : false;
						} else {
							mail_credit = false;
							sms_credit = false;
							notify_credit = false;
							alert_credit = false;
						}
					} else {
						mail_credit = true;
						sms_credit = true;
						notify_credit = true;
						alert_credit = true;
					}

					String alertName;
					if (nodeName != null) {
						alertName = nodeName + " is not reporting";
					} else {
						alertName = gatewayname + " is not reporting";
						if (optGatewayName != null && !optGatewayName.equalsIgnoreCase("null"))
							alertName = gatewayname + " " + optGatewayName.trim() + " is not Reporting";
					}
					String alertMsg = alertName + " due to network issue or low battery\n"
							+ helper.getNodeDetails(nodeName) + gatewayname + ": " + gatewayName + ",\nTime:"
							+ Helper.setDateAndTime(endDateTime) + helper.getTimeZone(timeZone);

					String curUTC = RemainderservicesUtil.getCurrentTimeUTC();
					String mobile = "NA";
					String mail = "NA";
					
					if (sms_credit || mail_credit || notify_credit || alert_credit) {
						int n_sms=0;
						int n_mail=0;
						int n_notify=0;
						
						
						if(sms_credit && SMS) {
							mobile = alertCfg.getMobilenos();
							sms_msg = alertMsg;
							n_sms =1;
						}
						if(mail_credit && EMAIL) {
							mail = alertCfg.getEmailids();
							email_msg = alertMsg;
							n_mail =1;
						}
						if(notify_credit && NOTIFY) {
							app_msg = alertMsg;
							n_notify =1;
						}
						
						alertServiceV4.UpdateAlertNotified(alertId, endDateTime, alertMsg,n_sms,n_mail,n_notify,"NA");

					/*	JScheduleAlert jalert = new JScheduleAlert(jUser.getUser_id(), alertId, assetId, companyId, 1,
								mobile, mail, curUTC, curUTC, "NA", curUTC, cmpName, gatewayName,
								sms_msg, email_msg, app_msg, alertCfg.getNotifyfreq(), alertCfgId, timeZone, 11, true,
								0, curUTC, featurecode_MAIL, featurecode_SMS, featurecode_NOTIFY,planVer, featurecode_ALERT,0,0);
						*/
						JScheduleAlert jalert = new JScheduleAlert();
						ImmediateAlert instant = new ImmediateAlert(userserviceV4, alertServiceV4, 
								deviceStatusService,scheduleAlert, iFirebaseService);

						boolean status = instant.sendInstantAlert(jalert, "immediate", ip, validate_auth.trim());
					}

					deviceStatusService.updateThrottlingCount(gatewayId, gatewayname, companyId, asset);

				}
			}
		} catch (Exception s) {
			log.error("updateNRAlert: " + s.getLocalizedMessage());
		}
	}

	public boolean notifyNode(IDeviceStatusService deviceStatusService, INodeService nodeService,
			IUserServiceV4 userserviceV4, IAlertServiceV4 alertServiceV4,IScheduleAlertService scheduleAlert,
			IFirebaseService iFirebaseService, String ip, long[] credit_points, String validate_auth) {
		long companyId = 0;
		long gatewayId = 0;
		long nodeId = 0;
		String gatewayName = null;
		String nodeName = null;
		String timeZone = null;
		log.info("notifyNode...");

		try {
			ArrayList<JLastNodeRpt> nodeList = nodeService.getLastNodeRpt();

			for (JLastNodeRpt lnr : nodeList) {

				Date eventDate = lnr.getDate();
				Time eventTime = lnr.getTime();
				timeZone = lnr.getTimezone();
				nodeId = lnr.getNode_id();
				String curDateTime = Helper.getCurrentDateTime(timeZone);

				if (eventDate == null || eventTime == null) {
					log.info("Node event date or event time is returned null");
					break;
				}
				log.info("event date = " + eventDate + " event time= " + eventTime);
				Date eventDateTime = helper.combineDateTime(eventDate, eventTime);
				Timestamp tsEventDateTime = new Timestamp(eventDateTime.getTime());

				log.info("tsEventDateTime = " + tsEventDateTime);

				if ((tsEventDateTime != null) && (nodeId >= 0)) {

					Calendar currDateCal = Calendar.getInstance();
					currDateCal.setTimeZone(TimeZone.getTimeZone("GMT" + timeZone));
					currDateCal.setTime(new Date(System.currentTimeMillis()));

					long curTimeMillis = currDateCal.getTimeInMillis();
					long rptTimeMillis = Helper.getTimestampInMillis(tsEventDateTime, "GMT" + timeZone);

					long diff = curTimeMillis - rptTimeMillis;

					if (diff >= ONE_HOUR) {
						log.info("Report time exceeded " + diff + " >= " + ONE_HOUR);
						nodeService.updateNodeStatus(nodeId, NOT_REPORTING);

						JNode node = nodeService.getNode(nodeId);

						if (node != null) {

							companyId = node.getCmp_id();
							gatewayId = node.getGatewayid();
							nodeName = node.getName();
							gatewayName = node.getGatewayname();
							log.info("gatewayId : " + gatewayId);

							boolean isNRAlert = deviceStatusService.isNRAlert(companyId, curDateTime, nodeId);
							if (isNRAlert == true)
								log.info("isNRAlert: " + isNRAlert);

							if (nodeService.isNodeEnabled(nodeId) == true && true == isNRAlert)
								updateNotReportingAlert(deviceStatusService, userserviceV4, alertServiceV4,
										scheduleAlert, iFirebaseService,
										companyId, nodeId, gatewayId, nodeId, gatewayName, nodeName,
										"Gateway", null, timeZone, tsEventDateTime, credit_points, ip, validate_auth,"NA");
						} else {
							log.info("isNRAlert: Statement returned null for query");
						}

					} else {
						log.info("Report time not exceeded " + diff + " < " + ONE_HOUR);
					}
				}
			}
		} catch (Exception e) {
			log.error("updateStatus:" + e.getLocalizedMessage());
			return false;
		}
		return true;
	}
}
