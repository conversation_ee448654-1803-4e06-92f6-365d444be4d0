package com.nimble.wagglealertservice.job;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.nimble.wagglealertservice.helper.Helper;

@Component
public class NotifySMSAlert {

	private static final Logger log = LogManager.getLogger(NotifySMSAlert.class);

	Helper helper = new Helper();

	private static final int DB_SMS_LENGTH = 225;
	private static long noOfEmailIdsAllowed = 0;
	private static long noOfMobileNosAllowed = 0;

//	public void notifySMSAlert(int companyId, String cmpName, int gatewayId, String gatewayName, String nodeName,
//			String alertName, float alertValue, String alertValueUnit, String startDateTime, String endDateTime,
//			long alertId, int severity, String timeZone, long alertCfgId, AlertCfgV4 alertcfg, String alertMsg,
//			JGateway asset, IUserServiceV4 userserviceV4,
//			IAlertServiceV4 alertServiceV4, long[] credit_points, String ip, boolean sms_credits,
//			boolean mail_credits, String planVer, IDeviceStatusService deviceStatusService, String validate_auth) {
//
//		SMSInterface smsGateway = null;
//		int notifyFreq = 0;
//		String notificationType = null;
//		String currentTime = Helper.getCurrentDateTime(timeZone);
//		boolean sentMsg = false;
//
//		long smsCreditPoints = credit_points[0];
//		long emailCreditPoints = credit_points[1];
//		long voiceCreditPoints = credit_points[2];
//		long pushNotifPoints = credit_points[3];
//
//		int sms_count = 0;
//		int mail_count = 0;
//
//		String featurecode_MAIL = "N_NETWORK_MAIL";
//		String featurecode_SMS = "N_NETWORK_SMS";
//		String featurecode_alert="N_NETWORK_COUNT";
//		log.info("notifySMSAlert ");
//
//		log.info("notifySMSAlert, AlertMsg : " + alertMsg);
//
//		try {
//
//			if (alertcfg != null) {
//				notifyFreq = alertcfg.getNotifyfreq();
//				notificationType = alertcfg.getNotificationtype();
//				String mobileNos = alertcfg.getMobilenos();
//				String emailids = alertcfg.getEmailids();
//				String country = alertcfg.getCountry();
//				if (country == null || country == "null") {
//					country = "NA";
//				}
//				boolean SMS = false, EMAIL = false, VOICE = false;
//				if (notificationType.charAt(0) == '1') {
//					SMS = sms_credits;
//				}
//				if (notificationType.charAt(1) == '1') {
//					EMAIL = mail_credits;
//				}
//				if (notificationType.charAt(2) == '1') {
//					VOICE = true;
//				}
//
//				String[] mblnos = mobileNos.split(",");
//				int noOfMobilesregistered = mblnos.length;
//
//				long noOfMblsEmails[] = userserviceV4.getNoOfEmailsAndMobilesConf(companyId);
//
//				if (noOfMblsEmails != null) {
//					noOfMobileNosAllowed = noOfMblsEmails[0];
//					noOfEmailIdsAllowed = noOfMblsEmails[1];
//					log.info("noOfMobileNosAllowed = " + noOfMobileNosAllowed + "  noOfEmailIdsAllowed="
//							+ noOfEmailIdsAllowed);
//				}
//
//				long totalSmsCreditsReq = 0;
//				int mobileNosLen = 0;
//				if (noOfMobilesregistered < noOfMobileNosAllowed) {
//					mobileNosLen = noOfMobilesregistered;
//				} else {
//					mobileNosLen = (int) noOfMobileNosAllowed;
//				}
//				totalSmsCreditsReq = mobileNosLen * smsCreditPoints;
//
//				boolean bExtraCredits = false;
//				long avlCredits = 0;
//
//				avlCredits = asset.getTotalAssignedCredits() - asset.getTotalCreditSpent();
//				if (avlCredits <= 0) {
//
//					avlCredits = asset.getTotAssigExCredits() - asset.getTotalExtraCreditsSpent();
//					log.info("Available extra credits = " + avlCredits);
//					bExtraCredits = true;
//				}
//
//				/* Send SMS */
//				if ((SMS && ip != null) && noOfMobileNosAllowed != 0 && (avlCredits >= totalSmsCreditsReq))// )
//
//				{
//					log.info("SMS to mobile nos***********mobileNosLen = " + mobileNosLen);
//					String mobileNo = "";
//					if (country.equalsIgnoreCase("US")) {
//						for (int i = 0; i < mobileNosLen; i++) {
//							mobileNo = 1 + mblnos[i];
//							log.info("Mobile Number:" + mobileNo);
//							smsGateway = new Plivo();
//							sentMsg = smsGateway.callIrisSmsAPI(mobileNo, alertMsg, companyId, cmpName, ip);
//							sms_count++;
//						}
//					} else if (country.equalsIgnoreCase("India")) {
//						for (int i = 0; i < mobileNosLen; i++) {
//							mobileNo = 91 + mblnos[i];
//							log.info("Mobile Number:" + mobileNo);
//							smsGateway = new Plivo();
//							sentMsg = smsGateway.callIrisSmsAPI(mobileNo, alertMsg, companyId, cmpName, ip);
//							sms_count++;
//						}
//					} else {
//						for (int i = 0; i < mobileNosLen; i++) {
//							mobileNo = mblnos[i].replace("-", "");
//							log.info("mobile number:" + mobileNo);
//							smsGateway = new Plivo();
//							sentMsg = smsGateway.callIrisSmsAPI(mobileNo, alertMsg, companyId, cmpName, ip);
//							sms_count++;
//						}
//					}
//
//					asset.setSmscnt(mobileNosLen);
//					log.info("asset.setCreditSpentValues - totalSmsCreditsReq = " + totalSmsCreditsReq);
//
//					if (bExtraCredits)
//						asset.setExtraCreditSpentValues(totalSmsCreditsReq);
//					else
//						asset.setCreditSpentValues(totalSmsCreditsReq);
//
//					log.info("Creditspent per day=" + asset.getCreditSpentperDay() + " extra credits spent per day = "
//							+ asset.getExCreditSpentPerDay());
//				}
//
//				/* Send PushNotification */
//				if (userserviceV4.getAppNotifyEnable(companyId)) {
//					bExtraCredits = false;
//					avlCredits = asset.getTotalAssignedCredits() - asset.getTotalCreditSpent();
//					if (avlCredits <= 0) {
//						avlCredits = asset.getTotAssigExCredits() - asset.getTotalExtraCreditsSpent();
//						bExtraCredits = true;
//					}
//					log.info("PUSH notification enabled for this company id- " + companyId);
//
//					log.info("Do App Notification" + "Gatewayid: " + gatewayId);
//					List<String> tokens = userserviceV4.getTokensByGatewayId(gatewayId);
//					String alert_msg = alertName;
//
//					if (alertValueUnit.contains("deg"))
//						alert_msg = alertName + " : " + getAlertValue(alertValue, alertValueUnit);
//					else if (alertValueUnit.equalsIgnoreCase("%"))
//						alert_msg = alertName + " : " + getAlertValue((int) alertValue, alertValueUnit);
//
//					if (tokens != null && tokens.size() > 0) {
//
//						String irisKey = "AIzaSyBjuMxkZVgIAPeQhPi5u9aVHkE8wkrrKVs";
//						String whitelabelKey = "AIzaSyCfKCTcPwjuJoJhh8eMYtpVIRwfyL4ED_g";
//						log.info("iriskey : " + irisKey);
//						log.info("whitelabelKey : " + whitelabelKey);
//						String pushNotifKey = irisKey;
//
//						// Check if company type is white label
//						int company_type = userserviceV4.getCompanyType(companyId);
//						if (company_type == 6) {
//							pushNotifKey = whitelabelKey;
//						}
//
//						Helper.callFCMAPI(alertId, alertName, alert_msg, gatewayName, tokens, pushNotifKey);
//						asset.setPushnotificationcnt(1);
//
//						if (bExtraCredits)
//							asset.setExtraCreditSpentValues(pushNotifPoints);
//						else
//							asset.setCreditSpentValues(pushNotifPoints);
//						log.info("Creditspent per day=" + asset.getCreditSpentperDay()
//								+ " extra credits spent per day = " + asset.getExCreditSpentPerDay());
//					}
//
//				} else {
//					log.info("App notify is not enabled");
//				}
//
//				String[] emails = emailids.split(",");
//				int noOfEmailsRegtd = emails.length;
//				int noOfMailIdstoBeSent = 0;
//				String configuredEmailIds = null;
//				if (noOfEmailsRegtd < noOfEmailIdsAllowed) {
//					noOfMailIdstoBeSent = noOfEmailsRegtd;
//				} else {
//					noOfMailIdstoBeSent = (int) noOfEmailIdsAllowed;
//				}
//
//				for (int i = 0; i < noOfMailIdstoBeSent; i++) {
//					if (configuredEmailIds == null)
//						configuredEmailIds = emails[i];
//					else
//						configuredEmailIds = configuredEmailIds + "," + emails[i];
//				}
//				long totalEmailCreditsReq = noOfMailIdstoBeSent * emailCreditPoints;
//
//				bExtraCredits = false;
//				avlCredits = asset.getTotalAssignedCredits() - asset.getTotalCreditSpent();
//				if (avlCredits <= 0) {
//					avlCredits = asset.getTotAssigExCredits() - asset.getTotalExtraCreditsSpent();
//					bExtraCredits = true;
//				}
//
//				/* Send Email */
//				if (EMAIL && ip != null && noOfEmailIdsAllowed != 0 && emailids != null
//						&& (avlCredits >= totalEmailCreditsReq)) {
//					boolean isRVEmailId = false;
//
//					int cmpTypeId = userserviceV4.getCompanyType(companyId);
//					if (cmpTypeId == 3)
//						isRVEmailId = true;
//					if (cmpTypeId == 3 && (!alertName.contains("is not reporting"))) {
//						String amazonLink = "\nHappy? Then we are too! We would love it if you share your experience with other pet parents on Amazon "
//								+ "http://bit.ly/petmonitor4g";
//						alertMsg = alertMsg + amazonLink;
//
//					}
//					mail_count = configuredEmailIds.split(",").length;
//					smsGateway = new Plivo();
//					smsGateway.callIrisEmailAPI(configuredEmailIds, alertName, alertMsg, ip, isRVEmailId);
//
//					asset.setEmailcnt(noOfMailIdstoBeSent);
//					log.info("asset.setCreditSpentValues - totalEmailCreditsReq = " + totalEmailCreditsReq);
//
//					if (bExtraCredits)
//						asset.setExtraCreditSpentValues(totalEmailCreditsReq);
//					else
//						asset.setCreditSpentValues(totalEmailCreditsReq);
//					log.info("Creditspent per day=" + asset.getCreditSpentperDay() + " extra credits spent per day = "
//							+ asset.getExCreditSpentPerDay());
//				}
//				long totalVoiceCreditsReq = mobileNosLen * voiceCreditPoints;
//
//				bExtraCredits = false;
//				avlCredits = asset.getTotalAssignedCredits() - asset.getTotalCreditSpent();
//				if (avlCredits <= 0) {
//					avlCredits = asset.getTotAssigExCredits() - asset.getTotalExtraCreditsSpent();
//					bExtraCredits = true;
//				}
//
//				/* Send Voice call */
//				if (VOICE && userserviceV4.isVoiceAlert(alertCfgId, startDateTime) && ip != null
//						&& noOfMobileNosAllowed != 0 && (avlCredits >= totalVoiceCreditsReq))
//
//				{
//					String mobileNo = null;
//					if (country.equalsIgnoreCase("US")) {
//						for (int i = 0; i < mobileNosLen; i++) {
//							log.info("Mobile Number:" + mblnos[i]);
//							mobileNo = "+1" + mblnos[i];
//							smsGateway = new Plivo();
//							smsGateway.SendVoiceMessage(mobileNo, getVoiceMsg(alertMsg), companyId, cmpName, ip);
//
//						}
//					} else if (country.equalsIgnoreCase("India")) {
//						for (int i = 0; i < mobileNosLen; i++) {
//							log.info("Mobile Number:" + mblnos[i]);
//							mobileNo = "+91" + mblnos[i];
//							smsGateway = new Plivo();
//							smsGateway.SendVoiceMessage(mobileNo, getVoiceMsg(alertMsg), companyId, cmpName, ip);
//
//						}
//					} else {
//						for (int i = 0; i < mobileNosLen; i++) {
//							log.info("mobile number:" + mblnos[i]);
//							mobileNo = "+" + mblnos[i].replace("-", ""); // add country code 91 with phone number
//							smsGateway = new Plivo();
//							smsGateway.SendVoiceMessage(mobileNo, getVoiceMsg(alertMsg), companyId, cmpName, ip);
//
//						}
//					}
//
//					asset.setVoicecnt(mobileNosLen);
//					log.info("asset.setCreditSpentValues - totalVoiceCreditsReq = " + totalVoiceCreditsReq);
//
//					if (bExtraCredits)
//						asset.setExtraCreditSpentValues(totalVoiceCreditsReq);
//					else
//						asset.setCreditSpentValues(totalVoiceCreditsReq);
//					log.info("Creditspent per day=" + asset.getCreditSpentperDay() + " extra credits spent per day = "
//							+ asset.getExCreditSpentPerDay());
//
//				}
//
////				if (!planVer.equalsIgnoreCase("v1")) {
////					ImmediateAlert.updateFeatureCredit(deviceStatusService, gatewayId, featurecode_SMS,
////							featurecode_MAIL, sms_credits, mail_credits, sms_count, mail_count, ip, validate_auth,featurecode_alert);
////				}
//
//				try {
//					if (true == sentMsg) {
//						JAlertNotifyHistory alertNH = alertServiceV4.getAlertnotifyhistory(alertId);
//
//						if (alertNH != null) {
//							long id = alertNH.getId();
//							int count = alertNH.getNotifiedCount();
//							timeZone = alertNH.getTimeZone();
//
//							alertServiceV4.UpdateAlertnotifyHistoryNotify(++count, currentTime, id, alertId,
//									alertMsg, timeZone);
//
//							log.info(gatewayId + " : " + "notifySMSAlert: 1 row updated in alertNotifyHistory table!");
//						} else {
//
//							int notifiedCount = 0;
//							if (true == sentMsg)
//								notifiedCount = 1;
//
//							if (alertMsg.length() > DB_SMS_LENGTH)
//								alertMsg = alertMsg.substring(0, DB_SMS_LENGTH);
//
//							alertServiceV4.InsertAlertnotifyHistoryNotify(alertId, alertCfgId, startDateTime,
//									endDateTime, notifyFreq, currentTime, timeZone, notifiedCount, alertMsg);
//
//						}
//
//					}
//
//				} catch (Exception e) {
//					log.error("notifySMSAlert: " + e);
//				}
//			} else {
//				log.info("No contacts are configured for gatewayId: " + gatewayId);
//				return;
//			}
//
//		} catch (Exception e) {
//			log.error("notifySMSAlert: " + e);
//		}
//
//	}

	private static String getAlertValue(float alertValue, String unit) {
		if (alertValue <= -100000 || alertValue >= 100000)
			return "Error";
		if (unit.equalsIgnoreCase("deg F"))
			return CelsiusToFahrenheit(alertValue) + " " + unit;
		return alertValue + " " + unit;
	}

	public static String getVoiceMsg(String msg) {

		String voiceMsg = null;

		try {

			String Header = "Nimble ";
			String splitMsg[] = msg.split(",");
			String alertName = splitMsg[0];
			String OutletName[] = splitMsg[1].split(":")[1].split("(?<=\\D)(?=\\d)|(?<=\\d)(?=\\D)");

			String tail = ".Please Check Message";

			voiceMsg = Header + alertName + " from" + OutletName[0] + tail;

		} catch (Exception e) {
			log.error("getVoiceMsg: " + e.getLocalizedMessage());
		}

		return voiceMsg + "." + voiceMsg + "." + voiceMsg;
	}

	private static float CelsiusToFahrenheit(float tempmvalIndegreeCelsius) {

		double degreeFahrenheit = Double.valueOf(tempmvalIndegreeCelsius).floatValue() * 9 / 5 + 32;
		double roundvalues = Math.round(degreeFahrenheit * 100.0) / 100.0;
		return (float) roundvalues;
	}

}
