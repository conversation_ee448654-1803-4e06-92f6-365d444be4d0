package com.nimble.wagglealertservice.job;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.InterruptableJob;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.nimble.wagglealertservice.entity.JScheduleAlert;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.IFirebaseService;
import com.nimble.wagglealertservice.service.IScheduleAlertService;
import com.nimble.wagglealertservice.service.IUserServiceV4;

public class PowerLossAlert extends QuartzJobBean implements InterruptableJob {

	private static final Logger log = LogManager.getLogger(PowerLossAlert.class);

	@Autowired
	IUserServiceV4 userserviceV4;

	@Autowired
	IScheduleAlertService scheduleAlert;

	@Autowired
	IAlertServiceV4 alertServiceV4;

	@Autowired
	IDeviceStatusService deviceStatusService;
	
	@Autowired
	IFirebaseService iFirebaseService;

	//private static final int DB_SMS_LENGTH = 225;
	
	@Value("${irisservice_url}")
	private String irisservice_url;
	
	@Value("${validate_auth}")
	private String validate_auth;

	@Override
	protected void executeInternal(JobExecutionContext context) throws JobExecutionException {

		log.info("PowerLossAlert begins...");

		JobKey key = context.getJobDetail().getKey();
		String schedulerName = key.getName();

		JScheduleAlert jAlert = scheduleAlert.getScheduleAlertByName(schedulerName);
		ImmediateAlert ia=new ImmediateAlert(userserviceV4, alertServiceV4, deviceStatusService, scheduleAlert, 
				iFirebaseService);
		ia.sendInstantAlert(jAlert, "PLPB", irisservice_url.trim(), validate_auth.trim());
	}

	@Override
	public void interrupt() throws UnableToInterruptJobException {
		log.info("Stopping thread... ");
	}
}
