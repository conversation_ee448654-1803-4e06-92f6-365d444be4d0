package com.nimble.wagglealertservice.controller;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.wagglealertservice.Util.RemainderservicesUtil;
import com.nimble.wagglealertservice.dto.AlertCfgV4;
import com.nimble.wagglealertservice.dto.JResponse;
import com.nimble.wagglealertservice.entity.JScheduleAlert;
import com.nimble.wagglealertservice.job.ExecuteNotify;
import com.nimble.wagglealertservice.job.ImmediateAlert;
import com.nimble.wagglealertservice.job.NotReportingAlert;
import com.nimble.wagglealertservice.job.PowerLossAlert;
import com.nimble.wagglealertservice.job.TempAlert;
import com.nimble.wagglealertservice.quartz.JobService;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IAsyncService;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.IFirebaseService;
import com.nimble.wagglealertservice.service.INodeService;
import com.nimble.wagglealertservice.service.IScheduleAlertService;
import com.nimble.wagglealertservice.service.IUserServiceV4;

@Controller
public class JobController {

	private static final Logger log = LogManager.getLogger(JobController.class);

	@Autowired
	@Lazy
	JobService jobService;

	@Autowired
	IScheduleAlertService scheduleAlert;

	@Autowired
	IAlertServiceV4 alertServiceV4;

	@Autowired
	IUserServiceV4 userserviceV4;

	@Autowired
	IDeviceStatusService deviceStatusService;
	
	@Autowired
	INodeService nodeService;
	
	@Autowired	
	private IAsyncService async;
	
	@Autowired
	IFirebaseService iFirebaseService;

	@Value("${pasttime}")
	private int pasttime;
	
	@Value("${ackalerttime}")
	private int ackalerttime;
	
	@Value("${notreportingtime}")
	private int notreportingtime;
	
	@Value("${validate_auth}")
	private String validate_auth;
	
	@Value("${irisservice_url}")
	private String irisservice_url;
	
	@Value("${alert_check}")
	private boolean alert_check=false;
	
	@Value("${push_notification}")
	private boolean push_notification=false;
	
	@Value("${email_notification}")
	private boolean email_notification=false;
	
	@Value("${alert_check_range}")
	private int alert_check_range = 0;
	
	@Value("${alert_pushNotificationId}")
	private int alert_pushNotificationId = 0;

    @Value("${checkIsLastCredit}")
    private boolean checkIsLastCredit;

	@RequestMapping(value = "v4.0/setschedulealert", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse scheduleAlert(@RequestBody JScheduleAlert alert, @RequestHeader String auth) {
		JResponse res = new JResponse();
		log.info("JobController :: setschedulealert..");
		try {
			
			if(!auth.equalsIgnoreCase(validate_auth)) {
				res.put("Status", 0);
				res.put("Msg", "Invalid Authkey");
				return res;
			}

			Class<? extends QuartzJobBean> jobClass = PowerLossAlert.class;

			String createdAt = RemainderservicesUtil.getCurrentTimeUTC();
			String updateAt = createdAt;

			String schedulerName = alert.getScheduler_name();
			String schedule_time = alert.getScheduler_time();
			String timeZone = alert.getTimezone();

			String schedulerTimeCheck = RemainderservicesUtil.getDelayTime(timeZone, pasttime);

			Date scheduleTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(schedule_time);

			alert.setCreated_at(createdAt);
			alert.setUpdated_at(updateAt);
			alert.setEnable(1);

			if (scheduleTime.before(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(schedulerTimeCheck))) {
				res.put("Status", 0);
				res.put("Msg", "Can't schedule alert for the past time");
				return res;
			}

			boolean status = scheduleAlert.createOrUpdateAlert(alert);

			if (status) {

				boolean scheduledCheck = jobService.scheduleOneTimeJob(schedulerName, jobClass, scheduleTime,
						"PLPBAlert");

				if (!scheduledCheck) {
					log.info("scheduler not created:"+alert.getGateway_id());
					res.put("Status", 0);
					res.put("Msg", "scheduler not created");
					return res;
				} else {
					log.info("scheduler created");
					res.put("Status", 1);
					res.put("Msg", "scheduler created");
					res.put("Scheduler_name", schedulerName);
					res.put("Scheduler_time", scheduleTime);
					return res;
				}
			} else {
				res.put("Status", 0);
				res.put("Msg", "schedulealert creation failed");
			}

		} catch (Exception e) {
			log.error("JobController :"+alert.getGateway_id() +":"+ e.getLocalizedMessage());
			res.put("Status", 0);
			res.put("Msg", e.getLocalizedMessage());
		}

		return res;

	}

	@RequestMapping(value = "v4.0/deleteschedulealert", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteScheduleAlert(@RequestParam("schedulername") String schedulerName) {
		JResponse res = new JResponse();
		log.info("JobController :: deleteschedulealert");
		try {
			boolean status = false;
			boolean isJobRunning = false;
			boolean updateStatus = false;

			if (jobService.isJobWithNamePresent(schedulerName, "PLPBAlert")) {
				isJobRunning = jobService.isJobRunning(schedulerName, "PLPBAlert");
				if (!isJobRunning)
					status = jobService.deleteJob(schedulerName, "PLPBAlert");
			}

			boolean isAlert = scheduleAlert.updateAlertByName(schedulerName, -1);

			if (isAlert) {

				JScheduleAlert jAlert = scheduleAlert.getScheduleAlertByName(schedulerName);
				long alertId = jAlert.getAlert_id();

//				updateStatus = notifyHistoryserviceV4.updateAlertForDelete(alertId);
//				log.info("updateAlertForDelete : " + updateStatus);

				updateStatus = alertServiceV4.DeleteplAlertHistoryStatus(alertId);
				log.info("Delete plAlertHistory alertId : "+alertId+" : " + updateStatus);
			}

			if (!isAlert) {
				res.put("Status", 0);
				res.put("Msg", "schedule alert not created or not updated");
				return res;
			} else {
				res.put("Status", 1);
				res.put("Msg", "schedule alert deleted");
				return res;
			}

		} catch (Exception e) {
			log.error("deleteScheduleAlert : schedulerName: "+schedulerName+":" + e.getLocalizedMessage());
			res.put("Status", 0);
			res.put("Msg", "Exception occurred");
			res.put("Error", e.getLocalizedMessage());
		}
		return res;
	}

	@RequestMapping(value = "v4.0/Schedulerdelete", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse daletejob() {
		JResponse res = new JResponse();
		log.info("JobController :: Schedulerdelete");
		boolean status = false;
		boolean isJobRunning = false;
		try {
			if (jobService.isJobWithNamePresent("NotReporting", "NotReporting")) {
				isJobRunning = jobService.isJobRunning("NotReporting", "NotReporting");
				log.info("Job running : " + isJobRunning);
				if (!isJobRunning) {
					status = jobService.deleteJob("NotReporting", "NotReporting");
					status = jobService.deleteJob("Ackalerts", "Ackalerts");
				}
			}

			if (status) {
				res.put("Status", 1);
				res.put("Msg", "Success");
				log.info("Job deleted");
			} else {
				res.put("Status", 0);
				res.put("Msg", "failure");
				res.put("isJobRunning", isJobRunning);
				log.info("Job is not deleted!!!");
			}

		} catch (Exception e) {
			res.put("Status", 0);
			res.put("Msg", "Error Occured");
			res.put("Error", e.getMessage());
			log.error("Error when deleting Job :" + e.getLocalizedMessage());
		}
		return res;
	}

	@RequestMapping(value = "v4.0/SchedulerCheck", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse reminderJob(@RequestParam("create") boolean create) {
		JResponse res = new JResponse();
		log.info("JobController :: SchedulerCheck");
		boolean status = false;
		try {
			if (!jobService.isJobWithNamePresent("Ackalerts", "Ackalerts") && create) {
				
				Date ackDate = RemainderservicesUtil.getDelayTime(Calendar.SECOND, 30);
				log.info("Ackalerts initial time : "+ackDate);
				status = jobService.scheduleRepeatJob("Ackalerts", ExecuteNotify.class, ackDate,
						"Ackalerts", "min", ackalerttime);
				log.info("Schedule ack alerts : "+status);
			
				Date notReportDate = RemainderservicesUtil.getDelayTime(Calendar.MINUTE, 5);
				log.info("Notreporting initial time : "+notReportDate);
				status = jobService.scheduleRepeatJob("NotReporting", NotReportingAlert.class, notReportDate, "NotReporting", "min", notreportingtime);
				log.info("Schedule NotReporting : "+status);
			}

			List<Map<String, Object>> quartzlist = jobService.getAllJobs("NotReporting", "NotReporting");

			res.put("Status", 1);
			res.put("Msg", "Success");
			res.put("created", status);
			res.put("Scheduler_List", quartzlist);
			log.info("Job scheduled");

		} catch (Exception e) {
			res.put("Status", 0);
			res.put("Msg", "Error Occured");
			res.put("Error", e.getMessage());
			log.error("Error when reminderJob : " + e.getLocalizedMessage());
		}
		return res;
	}

	@RequestMapping(value = "v4.0/immediatealert", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse immediatealert(@RequestBody JScheduleAlert alert, @RequestHeader String auth) {
		JResponse res = new JResponse();
		log.info("JobController :: immediatealert");
		try {

			if (!auth.equalsIgnoreCase(validate_auth)) {
				res.put("Status", 0);
				res.put("Msg", "Invalid Authkey");
				return res;
			}

			ImmediateAlert instant = new ImmediateAlert(userserviceV4, alertServiceV4, deviceStatusService,
					scheduleAlert, iFirebaseService);
			boolean isFlexiPlanPresent = userserviceV4.checkFlexiPlanHistory(alert.getGateway_id());
			boolean status = false;
			if(isFlexiPlanPresent) {
				status = instant.sendInstantAlert(alert, "immediate", irisservice_url.trim(), validate_auth.trim());
			}
			boolean isCriteriaCheck = false;

			// check marketing notification for temperature alerts once in 24hrs
			if (alert.getAlerttype_id() == 1l && alert_check) {

				AlertCfgV4 alertCfg = alertServiceV4.getAlertMinMax(alert.getAlertcfg_id());
				String remarks = "";

				if (alertCfg != null) {
					String tempUnit ="C";
					
					tempUnit = userserviceV4.getTempUnitByCmpy(alert.getCmp_id());
					NumberFormat formatter = new DecimalFormat("##.##");
					float min = Math.round(RemainderservicesUtil.CelsiusToFahrenheit(alertCfg.getMinval()));
					float max = Math.round(RemainderservicesUtil.CelsiusToFahrenheit(alertCfg.getMaxval()));
					float alertval = RemainderservicesUtil.CelsiusToFahrenheit(alert.getAlertvalue());
					
					float minDiff = Math.abs(min - alertval);
					float maxDiff = Math.abs(max - alertval);

					if ((0 < minDiff) && (minDiff <= alert_check_range)
							|| (0 < maxDiff) && (maxDiff <= alert_check_range)) {
						isCriteriaCheck = true;
						
						if (tempUnit.equalsIgnoreCase("C")) {
							min = Math.round(alertCfg.getMinval());
							max = Math.round(alertCfg.getMaxval());
							alertval = alert.getAlertvalue();
						}
						
						remarks = "Alert Condition(" + tempUnit + "):Min.Temp: " + formatter.format(min) + " and Max.Temp: " +formatter.format( max)
								+ "\nAlert value(" + tempUnit + "): " +formatter.format( alertval);
					}
					log.info("alert_check_range:" + alert_check_range + remarks);

					if (isCriteriaCheck) {
						async.updateGatewayShowVideo(alert.getGateway_id(), true);

						async.saveMarketingNotification(alert, remarks);
					}

					if (isCriteriaCheck && (push_notification || email_notification)) {
						async.sendMarketingNotification(alert, irisservice_url.trim(), validate_auth, push_notification,
								email_notification, alert_pushNotificationId,remarks, tempUnit,alert_check_range);
					}
				}
			}
            if (checkIsLastCredit) {
                async.sendLastCreditNotification(alert, irisservice_url.trim(), validate_auth, push_notification);
            }
			log.info("Immediate alert sent :alertId:" + alert.getAlert_id() + " : " + status);
			if (status) {
				res.put("Status", 1);
				res.put("Msg", "Success");
				return res;
			} else {
				res.put("Status", 0);
				res.put("Msg", "Failed");
				return res;
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error("immediatealert : alertId:" + alert.getAlert_id() + " : " + e.getLocalizedMessage());
			res.put("Status", 0);
			res.put("Msg", e.getMessage());
		}

		return res;

	}

	@RequestMapping(value = "v4.0/setTempAlertJob", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse setTempAlertJob() {
		JResponse res = new JResponse();
		log.info("JobController :: setTempAlertJob");
		boolean status = false;
		
		try {				
			if (!jobService.isJobWithNamePresent("TempAlertJob", "TempAlertJob")) {
				Calendar cal = Calendar.getInstance();
				cal.add(Calendar.DATE, 1);
				cal.set(Calendar.HOUR,6);
				cal.set(Calendar.MINUTE,0);
				cal.set(Calendar.SECOND,0);
				
				Date curDate = cal.getTime();
				status = jobService.scheduleTempAlertRepeatJobs("TempAlertJob", TempAlert.class, curDate);
				log.info("setTempAlertJob: "+status);
			}

			List<Map<String, Object>> quartzlist = jobService.getAllJobs("TempAlertJob", "TempAlertJob");

			res.put("Status", 1);
			res.put("Msg", "Success");
			res.put("created", status);
			res.put("Scheduler_List", quartzlist);
			log.info("Job scheduled");

		} catch (Exception e) {
			res.put("Status", 0);
			res.put("Msg", "Error Occured");
			res.put("Error", e.getMessage());
			log.error("Error in setTempAlertJob : " + e.getLocalizedMessage());
		}
		return res;
	}
	
//	@RequestMapping(value = "v4.0/notreporting", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse notreporting(@RequestBody JScheduleAlert alert, @RequestHeader String auth) {
//		JResponse res = new JResponse();
//		DeviceStatusNotify deviceStatusNotify = new DeviceStatusNotify();
//
//		log.info("NotReporting Job started...");
//
//			String ip = irisservice_url;// get ip
//			long credit_points[] = notifyserviceV4.getCreditPoints();// get creditpoints
//
//			deviceStatusNotify.notifyGateway(deviceStatusService, nodeService, notifyserviceV4, notifyHistoryserviceV4,
//					alertCfgServiceV4, ip, credit_points, validate_auth);
//		log.info("NotReporting Job completed");
//
//		return res;
//
//	}
}
