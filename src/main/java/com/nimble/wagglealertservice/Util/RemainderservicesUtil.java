package com.nimble.wagglealertservice.Util;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nimble.wagglealertservice.constant.RemainderservicesConstants;

public class RemainderservicesUtil {
	private static final Logger log = LogManager.getLogger(RemainderservicesUtil.class);
	
	public static String getDelayTime(String timeZone, int pasttime) {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.SECOND, -pasttime);
		Date d = calendar.getTime();
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		df.setTimeZone(TimeZone.getTimeZone(timeZone));
		String currentTime = df.format(d);
		return currentTime;
	}

	public static String getCurrentTimeUTC() {
		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat(RemainderservicesConstants.DATETIMEFORMAT);
		df.setTimeZone(TimeZone.getTimeZone(RemainderservicesConstants.UTCFORMAT));

		String currentTime = df.format(d);
		return currentTime;
	}

	public static String getCurrentDateTime(String timeFormat, String timezone) {
		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();
		SimpleDateFormat df = new SimpleDateFormat(timeFormat);
		df.setTimeZone(TimeZone.getTimeZone(timezone));
		String currentTime = df.format(d);
		return currentTime;
	}

	public static String getCurrentDateTime(String timeFormat, String timezone, int days) {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, days);
		Date d = calendar.getTime();
		SimpleDateFormat df = new SimpleDateFormat(timeFormat);
		df.setTimeZone(TimeZone.getTimeZone(timezone));
		String currentTime = df.format(d);
		return currentTime;
	}

	/**
	 * 
	 * returns the dateTime string in "yyyy-mm-dd HH:mm:ss" format
	 * 
	 * @param date - in ddmmyy format
	 * @param time - in HHmmss format
	 * @return the combined date and time parameters in "yyyy-mm-dd HH:mm:ss" format
	 */
	public static String getDateTime(String date, String time) {
		String dateYYYYMMDD;
		String dateTime;
		dateYYYYMMDD = new String(
				"20" + date.substring(4, 6) + "-" + date.substring(2, 4) + "-" + date.substring(0, 2));
		dateTime = new String(
				dateYYYYMMDD + " " + time.substring(0, 2) + ":" + time.substring(2, 4) + ":" + time.substring(4, 6));
		return dateTime;
	}

	public static Timestamp getDateTime_TS(String dateTime) {
		return Timestamp.valueOf(dateTime);
	}

	public static String getTime(String time) {
		String newtime = new String(time.substring(0, 2) + ":" + time.substring(2, 4) + ":" + time.substring(4, 6));
		return newtime;
	}

	public static String getUTCTime(String datetime, String timeZoneOffset) {

		SimpleDateFormat formatter = new SimpleDateFormat(RemainderservicesConstants.DATETIMEFORMAT);

		formatter.setTimeZone(TimeZone.getTimeZone("GMT" + timeZoneOffset));

		Date date = null;
		try {
			date = formatter.parse(datetime);
			SimpleDateFormat dateFormat = new SimpleDateFormat(RemainderservicesConstants.DATETIMEFORMAT);
			dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
			return dateFormat.format(date);
		} catch (ParseException e) {
			log.error(e.getLocalizedMessage());
			return null;
		}
	}
	
	public static Date getDelayTime(int delayType, int delaySeconds) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeZone(TimeZone.getTimeZone("UTC"));
		cal.add(delayType, delaySeconds);
		Date delayDate = cal.getTime();
		return delayDate;
	}
	
	public static float CelsiusToFahrenheit(float tempmvalIndegreeCelsius) {
		double degreeFahrenheit = Double.valueOf(tempmvalIndegreeCelsius).floatValue() * 9 / 5 + 32;
		double roundvalues = Math.round(degreeFahrenheit * 100.0) / 100.0;
		return (float) roundvalues;
	}
}
