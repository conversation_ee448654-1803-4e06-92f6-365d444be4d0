package com.nimble.wagglealertservice;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.RestController;

@RestController
@ComponentScan({ "com.nimble.wagglealertservice.job", "com.nimble.wagglealertservice", "com.nimble.wagglealertservice.service",
		"com.nimble.wagglealertservice.service.impl", "com.nimble.wagglealertservice.configuration",
		"com.nimble.wagglealertservice.controller", "com.nimble.wagglealertservice.entity",
		"com.nimble.wagglealertservice.dao", "com.nimble.wagglealertservice.dao.impl" })
@EnableAutoConfiguration(exclude = { HibernateJpaAutoConfiguration.class,
		org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration.class })
@ImportResource("classpath:spring.xml")
@EnableAsync
@SpringBootApplication
public class Application extends SpringBootServletInitializer {

	@Autowired
	public static ApplicationContext context;

	public static void main(String[] args) {
		context = SpringApplication.run(Application.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(Application.class);
	}

}
