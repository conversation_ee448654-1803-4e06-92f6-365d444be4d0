<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:sws="http://www.springframework.org/schema/web-services"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
        http://www.springframework.org/schema/web-services http://www.springframework.org/schema/web-services/web-services-2.0.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       ">
    <!-- <aop:aspectj-autoproxy />
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**" />
            <mvc:exclude-mapping path="/**" />
            <bean class="com.nimble.wagglealertservice.interceptor.RequestProcessingInterceptor"></bean>
        </mvc:interceptor>
    </mvc:interceptors> -->
	<!-- http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd -->
	<!-- Enable Annotation based Declarative Transaction Management -->
	<!-- <tx:annotation-driven transaction-manager="transactionManager" /> -->
	<!-- PropertyPlaceholderConfigurer for datasource.properties -->
		
	<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE" />
		<property name="searchSystemEnvironment" value="true" />
		<property name="location">
			<value>classpath:config.properties</value>
		</property>
	</bean>
		
	<bean id="irisDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
		<property name="driverClass" value="${database.driverClassName}"></property>
		<property name="jdbcUrl" value="${database.url}"></property>
		<property name="user" value="${database.username}"></property>
		<property name="password" value="${database.password}"></property>
		<!--Determines how many connections at a time c3p0 will try to acquire
			when the pool is exhausted. -->
		<property name="acquireIncrement" value="5" /> <!--Default: 3 -->
		<!--Defines how many times c3p0 will try to acquire a new Connection from
			the database before giving up. -->
		<property name="acquireRetryAttempts" value="30" /><!--Default:
			30 -->
		<!--Milliseconds, time c3p0 will wait between acquire attempts -->
		<property name="acquireRetryDelay" value="1000" /><!--Default:
			1000 Milliseconds -->
		<!--The default setting of caller is sometimes a problem when client applications
			will be hot redeployed by an app-server. When c3p0's Threads hold a reference
			to a contextClassLoader from the first client that hits them, it may be impossible
			to garbage collect a ClassLoader associated with that client when it is undeployed
			in a running VM. Setting this to library can resolve these issues. [See "Configuring
			To Avoid Memory Leaks On Hot Redeploy Of Client"] http://www.mchange.com/projects/c3p0/#configuring_to_avoid_memory_leaks_on_redeploy -->
		<property name="contextClassLoaderSource" value="library" />
		<!--Number of Connections a pool will try to acquire upon startup. Should
			be between minPoolSize and maxPoolSize. -->
		<property name="initialPoolSize" value="10" /><!--Default: 3 -->
		<!--Minimum number of Connections a pool will maintain at any given time -->
		<property name="minPoolSize" value="10" /><!--Default: 3 -->
		<!--Maximum number of Connections a pool will maintain at any given time. -->
		<property name="maxPoolSize" value="500" /><!--Default: 15 -->
		<!--c3p0 is very asynchronous. Slow JDBC operations are generally performed
			by helper threads that don't hold contended locks. Spreading these operations
			over multiple threads can significantly improve performance by allowing multiple
			operations to be performed simultaneously. -->
		<property name="numHelperThreads" value="40" /><!--Default: 3 -->
		<property name="maxStatements" value="5000" />
		<!--Seconds, maxIdleTime defines how many seconds a Connection should be
			permitted to go unused before being culled from the pool. Zero means idle
			connections never expire. -->
		<!--<property name="maxIdleTime" value="3600"/> --> <!--Default: 0 Seconds,(60 mins) -->
		<!--Seconds, maxConnectionAge forces the pool to cull any Connections that
			were acquired from the database more than the set number of seconds in the
			past. -->
		<!--<property name="maxConnectionAge" value="3600" /> --> <!--Default: 0 Seconds,(60 mins) -->
		<!--Number of seconds that Connections in excess of minPoolSize should
			be permitted to remain idle in the pool before being culled. -->
		<property name="maxIdleTimeExcessConnections" value="300" /><!--Default:
			0 Seconds,(5 mins) -->
		<!--If this is a number greater than 0, c3p0 will test all idle, pooled
			but unchecked-out connections, every this number of seconds. -->
		<property name="idleConnectionTestPeriod" value="600" /><!--Default:
			0 Seconds,(10 Mins) -->
		<!--Defines the query that will be executed for all connection tests, if
			the default ConnectionTester is being used. -->
		<property name="preferredTestQuery" value="SELECT 1" />
		<!--The number of milliseconds a client calling getConnection() will wait
			for a Connection to be checked-in or acquired when the pool is exhausted. -->
		<property name="checkoutTimeout" value="30000" /><!--Default: 0
			Seconds,(5 mins) -->
		<!--Seconds. If set, if an application checks out but then fails to check-in
			[i.e. close()] a Connection within the specified period of time, the pool
			will unceremoniously destroy() the Connection. -->
		<property name="unreturnedConnectionTimeout" value="300" /><!--Default:
			0 Seconds,(5 mins) -->
		<!--<property name="testConnectionOnCheckin" value="true" /> -->
		<!--Begin by setting testConnectionOnCheckout to true and get your application
			to run correctly and stably. If you are happy with your application's performance,
			you can stop here! This is the simplest, most reliable form of Connection-testing,
			but it does have a client-visible performance cost. -->
		<property name="testConnectionOnCheckout" value="true" />
		<!--<property name="connectionCustomizerClassName" value="com.bgt.lens.web.main.ConnectionPoolLogger"/> -->
	</bean>
	<bean id="sessionFactory"
		class="org.springframework.orm.hibernate4.LocalSessionFactoryBean">
		<property name="dataSource" ref="irisDataSource" />
		<property name="packagesToScan" value="com.nimble.wagglealertservice.entity" />
		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">${hibernate.dialect}</prop>
				<prop key="hibernate.show_sql">${hibernate.show_sql}</prop>
				<prop key="hibernate.format_sql">${hibernate.format_sql}</prop>
				<prop key="hibernate.generate_statistics">${hibernate.generate_statistics}</prop>
				<!-- <prop key="hibernate.current_session_context_class">thread</prop> -->
				<prop key="hibernate.event.merge.entity_copy_observer">allow</prop>
			</props>
		</property>
	</bean>
	<bean id="transactionManager"
		class="org.springframework.orm.hibernate4.HibernateTransactionManager"  primary="true">
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>	
    
</beans>