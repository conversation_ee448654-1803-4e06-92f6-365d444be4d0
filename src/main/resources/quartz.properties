org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 30
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread = true

org.quartz.scheduler.instanceId = AUTO

 #specify the jobstore used
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
org.quartz.jobStore.useProperties=false
org.quartz.jobStore.tablePrefix =ALERT_
org.quartz.jobStore.misfireThreshold = 60000
org.quartz.jobStore.isClustered = true

