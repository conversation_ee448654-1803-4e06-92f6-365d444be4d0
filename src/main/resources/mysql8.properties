#  MySQL Database Configuration
database.driverClassName=com.mysql.cj.jdbc.Driver

#development DB
#database.url=************************************
#database.username=root
#database.password=karthik1519
database.url=******************************************************************************************
database.username=wgaltsvc
database.password=ghVd$v3M0

#######################################################################
#  Hibernate Configuration
hibernate.dialect=org.hibernate.dialect.MySQLDialect

#######################################################################
# Hibernate General Configuration
hibernate.show_sql=false
hibernate.format_sql=false
hibernate.generate_statistics=false

#Reminder config
reminderLimit=20
enable_remainder=true
pasttime=60
ackalerttime=30
notreportingtime=45

#plivo Config
plivono=13308221830
enablePowerBack=false
powerBackUUID=291e241c-ef21-45f9-81e6-16f00d7da641

#validation authkey
validate_auth=8977b8da604d9779d51baf4aacabef5af2771bf7

#server ip address
irisservice_url=https://staging-api8.nimblepetapp.com

#Notification enable
push_notification=false
email_notification=false
alert_check_range=4
alert_check=true
alert_pushNotificationId=17

############################################################
#Asyn Thread Pool Configuration
aysnc.corePoolSize=30
aysnc.maxPoolSize=100
aysnc.queueCapacity=150

#CD-Team Mail ID
to_mail=<EMAIL>
cc_mail=<EMAIL>,<EMAIL>
report_mail_from_add=<EMAIL>