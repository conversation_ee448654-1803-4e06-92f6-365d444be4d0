<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
	<Appenders>
		<!-- <RollingRandomAccessFile name="ApplicationLogger" fileName="/opt/log/nimbleservices/nimbleservices.log"
			immediateFlush="true" filePattern="/opt/log/nimbleservices/nimbleservices.log-%d{yyyy-MM-dd-HH-mm-ss}.log"
			> -->
		<RollingRandomAccessFile
			name="ApplicationLogger"
			fileName="/opt/log/wagglealertservice/wagglealertservice.log"
			immediateFlush="true"
			filePattern="/opt/log/wagglealertservice/wagglealertservice.log-%d{yyyy-MM-dd-HH-mm-ss}.log">
			<PatternLayout>
				<pattern>%d|[%t]|%p|%c{1}:%L|%M|%m%n</pattern>
			</PatternLayout>
			<Policies>
				<SizeBasedTriggeringPolicy size="2 MB" />
			</Policies>
			<DefaultRolloverStrategy max="5" />
		</RollingRandomAccessFile>		
	</Appenders>
	<Loggers>
		<Logger name="ApplicationLogger" level="INFO"
			includeLocation="false" additivity="false">
			<appender-ref ref="ApplicationLogger" level="INFO" />
		</Logger>
		<!-- <Logger name="AdvanceLogger" level="INFO"
			includeLocation="true" additivity="false">
			<appender-ref ref="AdvanceLogger" level="INFO" />
		</Logger>
		<Logger name="AdvanceLogger2" level="INFO"
			includeLocation="true" additivity="false">
			<appender-ref ref="AdvanceLogger2" level="INFO" />
		</Logger>
		<Logger name="AdvanceLogger3" level="INFO"
			includeLocation="true" additivity="false">
			<appender-ref ref="AdvanceLogger3" level="INFO" />
		</Logger>
		<Logger name="AdvanceLogger4" level="INFO"
			includeLocation="true" additivity="false">
			<appender-ref ref="AdvanceLogger4" level="INFO" />
		</Logger> -->
		<Root level="INFO" includeLocation="false" additivity="false">
			<AppenderRef ref="ApplicationLogger" />
		</Root>
	</Loggers>
</Configuration>
