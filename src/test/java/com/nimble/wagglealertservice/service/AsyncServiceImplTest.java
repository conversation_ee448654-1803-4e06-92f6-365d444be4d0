package com.nimble.wagglealertservice.service;

import com.nimble.wagglealertservice.dao.IUserDaoV4;
import com.nimble.wagglealertservice.dto.JUser;
import com.nimble.wagglealertservice.entity.JScheduleAlert;
import org.apache.http.entity.StringEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.UnsupportedEncodingException;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class AsyncServiceImplTest {

    @Mock
    private IUserDaoV4 userserviceV4;

    @Mock
    private IDeviceStatusService deviceStatusService;

    @Mock
    private SMSInterface smsGateway;

    @InjectMocks
    private TestAsyncServiceImpl asyncServiceImpl;

    private final String server_ip = "127.0.0.1";
    private final String validate_auth = "8977b8da604d9779d51baf4aacabef5af2771bf7";
    private final boolean push_notification = true;

    private JScheduleAlert setJScheduleAlertObject() {
        JScheduleAlert jAlert = new JScheduleAlert();
        jAlert.setUser_id(1036L);
        jAlert.setAlert_id(1);
        jAlert.setGateway_id(1983L);
        jAlert.setCmp_id(1);
        jAlert.setEnable(1);
        jAlert.setMobilenos("1234567890");
        jAlert.setEmail("<EMAIL>");
        jAlert.setFeaturecode_SMS("featureXYZ");
        return jAlert;
    }

    private JUser setUserObject() {
        JUser juser = new JUser(1036L, "V2");
        juser.setAuthkey(validate_auth);
        return juser;
    }

    @BeforeEach
    void setup() {
        // Optional: setup common mocks or reset state
    }

    // Plan Version - Positive
    @Test
    void sendLastCreditNotificationT1() {
        JScheduleAlert alert = setJScheduleAlertObject();
        JUser juser = setUserObject();

        asyncServiceImpl.sendLastCreditNotification(alert, server_ip, validate_auth, push_notification);

        assertEquals("V2", alert.getPlan_ver());

        assertEquals(1036L, alert.getUser_id());
    }

    // Plan Version - Negative
    @Test
    void sendLastCreditNotificationT2() {
        JScheduleAlert alert = setJScheduleAlertObject();
        JUser juser = setUserObject();

        asyncServiceImpl.sendLastCreditNotification(alert, server_ip, validate_auth, push_notification);

        assertNotEquals("V3", alert.getPlan_ver());

        assertEquals(1036L, alert.getUser_id());
    }

    // Send PN
    @Test
    void sendLastCreditNotificationT3() {
        doReturn(true).when(deviceStatusService).checkLastCreditNotification(anyLong(),anyString() ,any(JUser.class));
        boolean result = deviceStatusService.checkLastCreditNotification(1983L,"feature_Code", setUserObject());
        assertTrue(result);
    }

    // Send PN - Negative
    @Test
    void sendLastCreditNotificationT4() {
        doReturn(false).when(deviceStatusService).checkLastCreditNotification(anyLong(), anyString(),any(JUser.class));
        boolean result = deviceStatusService.checkLastCreditNotification(1983L, "feature_Code",setUserObject());
        assertFalse(result);
    }

    // Get Push Notification ID - Positive
    @Test
    void sendLastCreditNotificationT5() {
        doReturn(1000L).when(deviceStatusService).getAlertPushNotificationId(anyString());
        long result = deviceStatusService.getAlertPushNotificationId("featureXYZ");
        assertEquals(1000L, result);
    }

    // Get Push Notification ID - Negative if no record found
    @Test
    void sendLastCreditNotificationT6() {
        doReturn(0L).when(deviceStatusService).getAlertPushNotificationId(anyString());
        long result = deviceStatusService.getAlertPushNotificationId("featureXYZ");
        assertEquals(0L, result);
    }

    @Test
    void sendLastCreditNotificationT7() throws UnsupportedEncodingException {

        doReturn(false).when(smsGateway).postAPIWithAuthHeader(anyString(), anyString(), any(StringEntity.class));

        assertFalse(smsGateway.postAPIWithAuthHeader("url", "auth", new StringEntity("json")));

    }

    @Test
    void sendLastCreditNotificationT8() throws UnsupportedEncodingException {

        doReturn(true).when(smsGateway).postAPIWithAuthHeader(anyString(), anyString(), any(StringEntity.class));
        assertTrue(smsGateway.postAPIWithAuthHeader("url", "auth", new StringEntity("json")));
    }
}