package com.nimble.wagglealertservice.job;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.nimble.wagglealertservice.dto.JFcmNotification;
import com.nimble.wagglealertservice.entity.JScheduleAlert;
import com.nimble.wagglealertservice.service.IAlertServiceV4;
import com.nimble.wagglealertservice.service.IDeviceStatusService;
import com.nimble.wagglealertservice.service.IFirebaseService;
import com.nimble.wagglealertservice.service.IScheduleAlertService;
import com.nimble.wagglealertservice.service.IUserServiceV4;
import com.nimble.wagglealertservice.service.SMSInterface;

@ExtendWith(MockitoExtension.class)
class ImmediateAlertTest {

    @Mock
    private IUserServiceV4 userServiceV4;

    @Mock
    private IAlertServiceV4 alertServiceV4;

    @Mock
    private IDeviceStatusService deviceStatusService;

    @Mock
    private IScheduleAlertService scheduleAlertService;

    @Mock
    private IFirebaseService firebaseService;

    @Mock
    private SMSInterface smsGateway;

    private ImmediateAlert immediateAlert;
    private JScheduleAlert jAlert;
    private final String serverIp = "http://test-server";
    private final String validateAuth = "test-auth";

    @BeforeEach
    void setUp() {
        immediateAlert = new ImmediateAlert(userServiceV4, alertServiceV4, deviceStatusService,
                scheduleAlertService, firebaseService);

        jAlert = new JScheduleAlert();
        jAlert.setAlert_id(1L);
        jAlert.setCmp_id(123L);
        jAlert.setCmp_name("Test Company");
        jAlert.setMobilenos("1234567890");
        jAlert.setSms_msg("Test SMS Message");
        jAlert.setEmail("<EMAIL>");
        jAlert.setApp_msg("Test Push Notification");
        jAlert.setNotificationtype("100"); // Only SMS enabled
        jAlert.setFeaturecode_MAIL("EMAIL_ALERT");
        jAlert.setFeaturecode_SMS("SMS_ALERT");
        jAlert.setFeaturecode_NOTIFY("NOTIFY_ALERT");
        jAlert.setFeaturecode_alert("ALERT_COUNT");
        jAlert.setAlert_enable(true);
    }

    @Test
    void testSendInstantAlert_OnlySmsEnabled() throws Exception {
        // Arrange
        SMSInterface smsGateway = mock(SMSInterface.class);

        when(smsGateway.callIrisSmsAPIReturnString(
                anyString(),  // phoneNo
                anyString(),  // msg
                anyInt(),     // cmpid
                anyString(),  // cmpname
                anyString(),  // ip
                anyString()   // validate_auth
        )).thenReturn("SMS_SENT");

        // Act
        boolean result = immediateAlert.sendInstantAlert(jAlert, "TEST", serverIp, validateAuth);

        // Assert
        verify(smsGateway).callIrisSmsAPIReturnString(
                eq(jAlert.getMobilenos()),
                eq(jAlert.getSms_msg()),
                eq((int)jAlert.getCmp_id()),
                eq(jAlert.getCmp_name()),
                eq(serverIp),
                eq(validateAuth)
        );
    }

    @Test
    void testSendInstantAlert_AllNotificationsEnabled() throws Exception {
        // Arrange
        jAlert.setNotificationtype("111"); // All notifications enabled

        when(smsGateway.callIrisSmsAPIReturnString(anyString(), anyString(), anyInt(), anyString(), anyString(), anyString()))
                .thenReturn("SMS_SENT");
        when(smsGateway.callIrisEmailAPI(anyString(), anyString(), anyString(), anyString(), anyBoolean(), anyString()))
                .thenReturn(true);
        when(firebaseService.sendNotification(any())).thenReturn(1);

        // Act
        boolean result = immediateAlert.sendInstantAlert(jAlert, "TEST", serverIp, validateAuth);

        // Assert
        assertTrue(result);
        verify(smsGateway).callIrisSmsAPIReturnString(
                eq(jAlert.getMobilenos()),
                eq(jAlert.getSms_msg()),
                eq((int)jAlert.getCmp_id()),
                eq(jAlert.getCmp_name()),
                eq(serverIp),
                eq(validateAuth)
        );
        verify(smsGateway).callIrisEmailAPI(
                eq(jAlert.getEmail()),
                anyString(),  // subject
                anyString(),  // body
                eq(serverIp),
                eq(true),     // isHtml
                eq(validateAuth)
        );
        verify(firebaseService).sendNotification(any());
    }
}