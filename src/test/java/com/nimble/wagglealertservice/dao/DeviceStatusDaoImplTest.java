package com.nimble.wagglealertservice.dao;

import com.nimble.wagglealertservice.dao.impl.DeviceStatusDaoImpl;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class DeviceStatusDaoImplTest {

    @Test
    void testCheckIsThreadSensorTrue() throws Exception {
        SessionFactory sessionFactory = Mockito.mock(SessionFactory.class);
        Session session = Mockito.mock(Session.class);
        Query query = Mockito.mock(SQLQuery.class);

        Mockito.when(sessionFactory.getCurrentSession()).thenReturn(session);
        Mockito.when(session.createSQLQuery(Mockito.anyString())).thenReturn((SQLQuery) query);
        Mockito.when(query.setParameter("gatewayId", 1L)).thenReturn(query);
        Mockito.when(query.uniqueResult()).thenReturn(true);

        DeviceStatusDaoImpl dao = new DeviceStatusDaoImpl();
        java.lang.reflect.Field field = DeviceStatusDaoImpl.class.getDeclaredField("sessionFactory");
        field.setAccessible(true);
        field.set(dao, sessionFactory);

        assertTrue(dao.checkIsThreadSensor(1L));
    }

    @Test
    void testCheckIsThreadSensorFalse() throws Exception {
        SessionFactory sessionFactory = Mockito.mock(SessionFactory.class);
        Session session = Mockito.mock(Session.class);
        Query query = Mockito.mock(SQLQuery.class);

        Mockito.when(sessionFactory.getCurrentSession()).thenReturn(session);
        Mockito.when(session.createSQLQuery(Mockito.anyString())).thenReturn((SQLQuery) query);
        Mockito.when(query.setParameter("gatewayId", 2L)).thenReturn(query);
        Mockito.when(query.uniqueResult()).thenReturn(false);

        DeviceStatusDaoImpl dao = new DeviceStatusDaoImpl();
        java.lang.reflect.Field field = DeviceStatusDaoImpl.class.getDeclaredField("sessionFactory");
        field.setAccessible(true);
        field.set(dao, sessionFactory);

        assertFalse(dao.checkIsThreadSensor(2L));
    }

    @Test
    void testCheckIsThreadSensor_Exception() throws Exception {
        SessionFactory sessionFactory = Mockito.mock(SessionFactory.class);
        Session session = Mockito.mock(Session.class);
        Query query = Mockito.mock(SQLQuery.class);

        Mockito.when(sessionFactory.getCurrentSession()).thenReturn(session);
        Mockito.when(session.createSQLQuery(Mockito.anyString())).thenReturn((SQLQuery) query);
        Mockito.when(query.setParameter(Mockito.anyString(), Mockito.anyLong())).thenReturn(query);
        Mockito.when(query.uniqueResult()).thenThrow(new RuntimeException("DB error"));

        DeviceStatusDaoImpl dao = new DeviceStatusDaoImpl();
        java.lang.reflect.Field field = DeviceStatusDaoImpl.class.getDeclaredField("sessionFactory");
        field.setAccessible(true);
        field.set(dao, sessionFactory);

        assertFalse(dao.checkIsThreadSensor(123L));
    }

    @Test
    void testGetHubIdForSensor_ReturnsValue() throws Exception {
        SessionFactory sessionFactory = Mockito.mock(SessionFactory.class);
        Session session = Mockito.mock(Session.class);
        Query query = Mockito.mock(SQLQuery.class);

        Mockito.when(sessionFactory.getCurrentSession()).thenReturn(session);
        Mockito.when(session.createSQLQuery(Mockito.anyString())).thenReturn((SQLQuery) query);
        Mockito.when(query.setParameter("nodeId", 10L)).thenReturn(query);
        Mockito.when(query.uniqueResult()).thenReturn(BigInteger.valueOf(123L)); // Correct type

        DeviceStatusDaoImpl dao = new DeviceStatusDaoImpl();
        java.lang.reflect.Field field = DeviceStatusDaoImpl.class.getDeclaredField("sessionFactory");
        field.setAccessible(true);
        field.set(dao, sessionFactory);

        assertEquals(123L, dao.getHubIdForSensor(10L));
    }

    @Test
    void testGetHubIdForSensor_Exception() throws Exception {
        SessionFactory sessionFactory = Mockito.mock(SessionFactory.class);
        Session session = Mockito.mock(Session.class);
        Query query = Mockito.mock(SQLQuery.class);

        Mockito.when(sessionFactory.getCurrentSession()).thenReturn(session);
        Mockito.when(session.createSQLQuery(Mockito.anyString())).thenReturn((SQLQuery) query);
        Mockito.when(query.setParameter(Mockito.anyString(), Mockito.anyLong())).thenReturn(query);
        Mockito.when(query.uniqueResult()).thenThrow(new RuntimeException("DB error"));

        DeviceStatusDaoImpl dao = new DeviceStatusDaoImpl();
        java.lang.reflect.Field field = DeviceStatusDaoImpl.class.getDeclaredField("sessionFactory");
        field.setAccessible(true);
        field.set(dao, sessionFactory);

        assertEquals(0L, dao.getHubIdForSensor(99L));
    }
}
